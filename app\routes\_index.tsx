import { useState, useEffect } from "react";
import { localStorageUtils, clipboardUtils, type PropertyData } from "~/utils/property-search";

export default function Index() {
  const [properties, setProperties] = useState<PropertyData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从localStorage加载数据
  useEffect(() => {
    const savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []);

  // 保存到localStorage
  useEffect(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);

  const handleSearchClick = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 读取剪贴板内容
      const clipboardText = await clipboardUtils.readText();
      
      if (!clipboardText.trim()) {
        throw new Error('剪贴板内容为空');
      }

      console.log('剪贴板内容:', clipboardText);

      // 调用API进行搜索和抓取
      const response = await fetch('/api/search-property', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ address: clipboardText.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        // 添加新的房产数据到列表开头
        setProperties(prev => [result.data, ...prev]);
      } else {
        throw new Error(result.error || '搜索失败');
      }

    } catch (error) {
      console.error('搜索失败:', error);
      setError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProperty = (index: number) => {
    setProperties(prev => prev.filter((_, i) => i !== index));
  };

  const buttonStyle = {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    border: 'none',
    padding: '15px 30px',
    fontSize: '18px',
    borderRadius: '8px',
    cursor: isLoading ? 'not-allowed' : 'pointer',
    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
    minWidth: '200px',
    opacity: isLoading ? 0.7 : 1,
  };

  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '20px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  };

  const errorStyle = {
    background: '#fee',
    color: '#c33',
    padding: '10px',
    borderRadius: '4px',
    marginTop: '10px',
    border: '1px solid #fcc',
  };

  const cardStyle = {
    background: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    marginBottom: '20px',
    overflow: 'hidden' as const,
  };

  const headerStyle = {
    background: '#f8f9fa',
    padding: '20px',
    borderBottom: '1px solid #eee',
  };

  const tableStyle = {
    width: '100%',
    borderCollapse: 'collapse' as const,
    margin: '20px 0',
    fontSize: '14px',
  };

  const thStyle = {
    padding: '12px 8px',
    textAlign: 'left' as const,
    borderBottom: '1px solid #eee',
    background: '#f8f9fa',
    fontWeight: 600,
    color: '#2c5aa0',
  };

  const tdStyle = {
    padding: '12px 8px',
    textAlign: 'left' as const,
    borderBottom: '1px solid #eee',
    maxWidth: '120px',
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis',
  };

  return (
    <div style={containerStyle}>
      <div style={{ textAlign: 'center', marginBottom: '30px' }}>
        <h1 style={{ color: '#2c5aa0', marginBottom: '10px' }}>🏠 房产信息搜索</h1>
        <p style={{ color: '#666', fontSize: '16px' }}>点击按钮从剪贴板读取地址并搜索房产信息</p>
      </div>

      <div style={{ textAlign: 'center', marginBottom: '40px' }}>
        <button 
          style={buttonStyle}
          onClick={handleSearchClick}
          disabled={isLoading}
        >
          {isLoading ? '🔍 搜索中...' : '📋 从剪贴板搜索'}
        </button>
        
        {error && (
          <div style={errorStyle}>
            ❌ {error}
          </div>
        )}
      </div>

      {properties.length > 0 && (
        <div>
          <h2 style={{ color: '#2c5aa0', marginBottom: '20px' }}>搜索结果 ({properties.length})</h2>
          
          {properties.map((property, index) => (
            <div key={property.id} style={cardStyle}>
              <div style={headerStyle}>
                <h3 style={{ margin: '0 0 10px 0', color: '#2c5aa0', fontSize: '20px' }}>{property.address}</h3>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '14px', color: '#666' }}>
                  <div>
                    <span style={{ marginRight: '15px' }}>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>
                    <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>
                  </div>
                  <button 
                    style={{
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '5px 10px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                    onClick={() => handleDeleteProperty(index)}
                    title="删除这条记录"
                  >
                    🗑️
                  </button>
                </div>
              </div>

              <div style={{ overflowX: 'auto', padding: '0 20px' }}>
                <table style={tableStyle}>
                  <thead>
                    <tr>
                      <th style={thStyle}>网站</th>
                      <th style={thStyle}>房型</th>
                      <th style={thStyle}>卧室</th>
                      <th style={thStyle}>浴室</th>
                      <th style={thStyle}>停车</th>
                      <th style={thStyle}>建成年份</th>
                      <th style={thStyle}>指导价</th>
                      <th style={thStyle}>估价范围</th>
                      <th style={thStyle}>拍卖日期</th>
                    </tr>
                  </thead>
                  <tbody>
                    {property.properties.map((prop, propIndex) => (
                      <tr key={propIndex}>
                        <td style={tdStyle}>
                          <a href={prop.sourceUrl} target="_blank" rel="noopener noreferrer" style={{ color: '#007bff', textDecoration: 'none' }}>
                            {prop.siteName}
                          </a>
                        </td>
                        <td style={tdStyle}>{prop.propertyType}</td>
                        <td style={tdStyle}>{prop.bedrooms}</td>
                        <td style={tdStyle}>{prop.bathrooms}</td>
                        <td style={tdStyle}>{prop.parking}</td>
                        <td style={tdStyle}>{prop.yearBuilt}</td>
                        <td style={tdStyle}>{prop.currentGuidePrice}</td>
                        <td style={tdStyle}>{prop.estimatedValueRange}</td>
                        <td style={tdStyle}>{prop.auctionDate}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <details style={{ padding: '20px', borderTop: '1px solid #eee' }}>
                <summary style={{ cursor: 'pointer', fontWeight: 600, color: '#2c5aa0', marginBottom: '15px' }}>
                  查看详细信息
                </summary>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginTop: '15px' }}>
                  {property.properties.map((prop, propIndex) => (
                    <div key={propIndex} style={{ 
                      background: '#f8f9fa', 
                      padding: '15px', 
                      borderRadius: '6px', 
                      borderLeft: '4px solid #2c5aa0' 
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', color: '#2c5aa0' }}>{prop.siteName}</h4>
                      <div>
                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                          <strong style={{ color: '#333' }}>地址:</strong> {prop.address}
                        </p>
                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                          <strong style={{ color: '#333' }}>土地面积:</strong> {prop.landSize}
                        </p>
                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                          <strong style={{ color: '#333' }}>建筑面积:</strong> {prop.buildingSize}
                        </p>
                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                          <strong style={{ color: '#333' }}>估价中位数:</strong> {prop.estimatedValueMid}
                        </p>
                        {prop.features.length > 0 && (
                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                            <strong style={{ color: '#333' }}>特色:</strong> {prop.features.join(', ')}
                          </p>
                        )}
                        {prop.inspectionTimes.length > 0 && (
                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                            <strong style={{ color: '#333' }}>开放检查:</strong> {prop.inspectionTimes.join(', ')}
                          </p>
                        )}
                        {prop.contact !== 'N/A' && (
                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                            <strong style={{ color: '#333' }}>联系方式:</strong> {prop.contact}
                          </p>
                        )}
                        {prop.description !== 'N/A' && (
                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>
                            <strong style={{ color: '#333' }}>描述:</strong> {prop.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </details>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}