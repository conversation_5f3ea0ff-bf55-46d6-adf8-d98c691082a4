import { useState, useEffect } from "react";
import { localStorageUtils, clipboardUtils } from "~/utils/property-search";
import type { PropertyData } from "~/utils/property-search";

export default function Index() {
  const [properties, setProperties] = useState<PropertyData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从localStorage加载数据
  useEffect(() => {
    const savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []);

  // 保存到localStorage
  useEffect(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);

  const handleSearchClick = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 读取剪贴板内容
      const clipboardText = await clipboardUtils.readText();
      
      if (!clipboardText.trim()) {
        throw new Error('剪贴板内容为空');
      }

      console.log('剪贴板内容:', clipboardText);

      // 调用API进行搜索和抓取
      const response = await fetch('/api/search-property', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ address: clipboardText.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        // 添加新的房产数据到列表开头
        setProperties(prev => [result.data, ...prev]);
      } else {
        throw new Error(result.error || '搜索失败');
      }

    } catch (error) {
      console.error('搜索失败:', error);
      setError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProperty = (index: number) => {
    setProperties(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div>
      <style dangerouslySetInnerHTML={{__html: `
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .header {
          text-align: center;
          margin-bottom: 30px;
        }

        .header h1 {
          color: #2c5aa0;
          margin-bottom: 10px;
        }

        .header p {
          color: #666;
          font-size: 16px;
        }

        .search-section {
          text-align: center;
          margin-bottom: 40px;
        }

        .search-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 18px;
          border-radius: 8px;
          cursor: pointer;
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
          min-width: 200px;
        }

        .search-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .search-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .search-button.loading {
          animation: pulse 1s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }

        .error-message {
          background: #fee;
          color: #c33;
          padding: 10px;
          border-radius: 4px;
          margin-top: 10px;
          border: 1px solid #fcc;
        }

        .results-section h2 {
          color: #2c5aa0;
          margin-bottom: 20px;
        }

        .property-card {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin-bottom: 20px;
          overflow: hidden;
        }

        .property-header {
          background: #f8f9fa;
          padding: 20px;
          border-bottom: 1px solid #eee;
        }

        .property-header h3 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
          font-size: 20px;
        }

        .property-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #666;
          flex-wrap: wrap;
          gap: 10px;
        }

        .property-meta span {
          margin-right: 15px;
        }

        .delete-button {
          background: #dc3545;
          color: white;
          border: none;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }

        .delete-button:hover {
          background: #c82333;
        }

        .property-table-container {
          overflow-x: auto;
          padding: 20px;
        }

        .property-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 14px;
        }

        .property-table th,
        .property-table td {
          padding: 12px 8px;
          text-align: left;
          border-bottom: 1px solid #eee;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }

        .property-table th {
          background: #f8f9fa;
          font-weight: 600;
          color: #2c5aa0;
        }

        .property-table a {
          color: #007bff;
          text-decoration: none;
        }

        .property-table a:hover {
          text-decoration: underline;
        }

        .property-details {
          padding: 20px;
          border-top: 1px solid #eee;
        }

        .property-details summary {
          cursor: pointer;
          font-weight: 600;
          color: #2c5aa0;
          margin-bottom: 15px;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-top: 15px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2c5aa0;
        }

        .detail-card h4 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
        }

        .detail-info p {
          margin: 5px 0;
          font-size: 14px;
          line-height: 1.4;
        }

        .detail-info strong {
          color: #333;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
          .container {
            padding: 10px;
          }

          .property-table-container {
            padding: 10px;
          }

          .property-table {
            font-size: 12px;
          }

          .property-table th,
          .property-table td {
            padding: 8px 4px;
          }

          .details-grid {
            grid-template-columns: 1fr;
          }

          .property-meta {
            flex-direction: column;
            align-items: flex-start;
          }

          .search-button {
            width: 100%;
            max-width: 300px;
          }
        }
      `}} />
      
      <div className="container">
        <div className="header">
          <h1>🏠 房产信息搜索</h1>
          <p>点击按钮从剪贴板读取地址并搜索房产信息</p>
        </div>

        <div className="search-section">
          <button 
            className={`search-button ${isLoading ? 'loading' : ''}`}
            onClick={handleSearchClick}
            disabled={isLoading}
          >
            {isLoading ? '🔍 搜索中...' : '📋 从剪贴板搜索'}
          </button>
          
          {error && (
            <div className="error-message">
              ❌ {error}
            </div>
          )}
        </div>

        {properties.length > 0 && (
          <div className="results-section">
            <h2>搜索结果 ({properties.length})</h2>
            
            {properties.map((property, index) => (
              <div key={property.id} className="property-card">
                <div className="property-header">
                  <h3>{property.address}</h3>
                  <div className="property-meta">
                    <span>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>
                    <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>
                    <button 
                      className="delete-button"
                      onClick={() => handleDeleteProperty(index)}
                      title="删除这条记录"
                    >
                      🗑️ 删除
                    </button>
                  </div>
                </div>

                <div className="property-table-container">
                  <table className="property-table">
                    <thead>
                      <tr>
                        <th>网站</th>
                        <th>房型</th>
                        <th>卧室</th>
                        <th>浴室</th>
                        <th>停车</th>
                        <th>建成年份</th>
                        <th>指导价</th>
                        <th>估价范围</th>
                        <th>拍卖日期</th>
                      </tr>
                    </thead>
                    <tbody>
                      {property.properties.map((prop, propIndex) => (
                        <tr key={propIndex}>
                          <td>
                            <a href={prop.sourceUrl} target="_blank" rel="noopener noreferrer">
                              {prop.siteName}
                            </a>
                          </td>
                          <td>{prop.propertyType}</td>
                          <td>{prop.bedrooms}</td>
                          <td>{prop.bathrooms}</td>
                          <td>{prop.parking}</td>
                          <td>{prop.yearBuilt}</td>
                          <td>{prop.currentGuidePrice}</td>
                          <td>{prop.estimatedValueRange}</td>
                          <td>{prop.auctionDate}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 详细信息折叠区域 */}
                <details className="property-details">
                  <summary>查看详细信息</summary>
                  <div className="details-grid">
                    {property.properties.map((prop, propIndex) => (
                      <div key={propIndex} className="detail-card">
                        <h4>{prop.siteName}</h4>
                        <div className="detail-info">
                          <p><strong>地址:</strong> {prop.address}</p>
                          <p><strong>土地面积:</strong> {prop.landSize}</p>
                          <p><strong>建筑面积:</strong> {prop.buildingSize}</p>
                          <p><strong>估价中位数:</strong> {prop.estimatedValueMid}</p>
                          {prop.features.length > 0 && (
                            <p><strong>特色:</strong> {prop.features.join(', ')}</p>
                          )}
                          {prop.inspectionTimes.length > 0 && (
                            <p><strong>开放检查:</strong> {prop.inspectionTimes.join(', ')}</p>
                          )}
                          {prop.contact !== 'N/A' && (
                            <p><strong>联系方式:</strong> {prop.contact}</p>
                          )}
                          {prop.description !== 'N/A' && (
                            <p><strong>描述:</strong> {prop.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </details>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}