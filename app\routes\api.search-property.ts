import { json } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { executeSearchWorkflow } from "~/utils/server-search-simple";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const { address } = await request.json();
    
    if (!address || typeof address !== 'string') {
      return json({ error: "Invalid address provided" }, { status: 400 });
    }

    console.log(`🔍 API: 开始处理房产地址: ${address}`);
    
    // 执行完整的搜索工作流
    const result = await executeSearchWorkflow(address);
    
    if (result.success) {
      return json(result);
    } else {
      return json(result, { status: 500 });
    }
    
  } catch (error) {
    console.error('❌ API: 工作流程失败:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}