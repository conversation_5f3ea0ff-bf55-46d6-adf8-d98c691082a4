// 将 exa-search.js 转换为 TypeScript 模块
// Environment variables are handled by Remix automatically

import { Exa } from 'exa-js';

// 初始化Exa客户端
const exa = new Exa(process.env.EXA_API_KEY!);

interface SearchOptions {
  numResults?: number;
  useAutoprompt?: boolean;
  type?: 'neural' | 'keyword';
  includeDomains?: string[];
  excludeDomains?: string[];
  startCrawlDate?: string | null;
  endCrawlDate?: string | null;
  includeText?: boolean;
}

interface SearchResult {
  success: boolean;
  searchQuery: string;
  totalResults: number;
  searchTime: number;
  results: any[];
  metadata: any;
  error?: string;
}

/**
 * 使用EXA搜索指定房产地址的相关信息
 */
export async function searchPropertyInfo(propertyAddress: string, options: SearchOptions = {}): Promise<SearchResult> {
    // 检查API密钥
    if (!process.env.EXA_API_KEY) {
        throw new Error('EXA_API_KEY 环境变量未设置。请在 .env 文件中添加: EXA_API_KEY=your_api_key_here');
    }

    // 默认选项
    const {
        numResults = 5,
        useAutoprompt = true,
        type = 'neural',
        includeDomains = [
            'domain.com.au',
            'realestate.com.au', 
            'realtor.com',
            'view.com.au',
            'onthehouse.com.au',
            'propertyvalue.com.au',
            'reiwa.com.au',
            'allhomes.com.au',
            'findbesthouse.com'
        ],
        excludeDomains = [],
        startCrawlDate = null,
        endCrawlDate = null,
        includeText = true
    } = options;

    // 构建搜索查询
    const searchQuery = `${propertyAddress} property real estate listing auction sale price estimate history`;

    console.log(`🔍 使用EXA搜索房产信息: ${propertyAddress}`);
    console.log(`📝 搜索查询: ${searchQuery}`);
    console.log(`🎯 目标结果数量: ${numResults}`);
    console.log(`🌐 包含域名: ${includeDomains.join(', ')}`);

    try {
        // 构建搜索参数
        const searchParams: any = {
            query: searchQuery,
            numResults: numResults,
            useAutoprompt: useAutoprompt,
            type: type,
            includeText: includeText
        };

        // 添加域名过滤
        if (includeDomains && includeDomains.length > 0) {
            searchParams.includeDomains = includeDomains;
        }
        if (excludeDomains && excludeDomains.length > 0) {
            searchParams.excludeDomains = excludeDomains;
        }

        // 添加日期过滤
        if (startCrawlDate) {
            searchParams.startCrawlDate = startCrawlDate;
        }
        if (endCrawlDate) {
            searchParams.endCrawlDate = endCrawlDate;
        }

        console.log('⏳ 正在执行EXA搜索...');
        const startTime = Date.now();

        // 执行搜索
        const searchResults = await exa.search(searchParams);
        
        const searchTime = Date.now() - startTime;
        console.log(`✅ EXA搜索完成 (${searchTime}ms)`);
        console.log(`📊 找到 ${searchResults.results.length} 个结果`);

        // 处理搜索结果
        const processedResults = searchResults.results.map((result: any) => ({
            title: result.title,
            url: result.url,
            publishedDate: result.publishedDate,
            author: result.author,
            score: result.score,
            text: result.text,
            highlights: result.highlights || [],
            relevanceScore: result.score
        }));

        // 按相关性得分排序
        processedResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

        // 显示结果摘要
        console.log('\n📋 EXA搜索结果摘要:');
        processedResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result.title}`);
            console.log(`   🔗 ${result.url}`);
            console.log(`   📊 相关性: ${(result.relevanceScore * 100).toFixed(1)}%`);
            if (result.publishedDate) {
                console.log(`   📅 发布日期: ${result.publishedDate}`);
            }
            console.log('');
        });

        return {
            success: true,
            searchQuery: searchQuery,
            totalResults: searchResults.results.length,
            searchTime: searchTime,
            results: processedResults,
            metadata: {
                requestId: searchResults.requestId || null,
                searchTime: searchTime,
                timestamp: new Date().toISOString(),
                propertyAddress: propertyAddress,
                searchParams: searchParams
            }
        };

    } catch (error) {
        console.error('❌ EXA搜索失败:', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
            success: false,
            error: errorMessage,
            searchQuery: searchQuery,
            results: [],
            totalResults: 0,
            searchTime: 0,
            metadata: {
                timestamp: new Date().toISOString(),
                propertyAddress: propertyAddress,
                errorDetails: error instanceof Error ? error.stack : String(error)
            }
        };
    }
}

/**
 * 搜索多个房产地址的信息
 */
export async function searchMultipleProperties(propertyAddresses: string[], options: SearchOptions = {}): Promise<SearchResult[]> {
    console.log(`🏘️ 批量搜索 ${propertyAddresses.length} 个房产地址`);
    
    const results: SearchResult[] = [];
    
    for (let i = 0; i < propertyAddresses.length; i++) {
        const address = propertyAddresses[i];
        console.log(`\n📍 [${i + 1}/${propertyAddresses.length}] 搜索: ${address}`);
        
        try {
            const result = await searchPropertyInfo(address, options);
            results.push(result);
            
            // 添加延迟避免API限制
            if (i < propertyAddresses.length - 1) {
                console.log('⏳ 等待中...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        } catch (error) {
            console.error(`❌ 搜索失败 [${address}]:`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            results.push({
                success: false,
                error: errorMessage,
                results: [],
                searchQuery: '',
                totalResults: 0,
                searchTime: 0,
                metadata: {
                    propertyAddress: address,
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    
    console.log(`\n✅ 批量搜索完成！成功: ${results.filter(r => r.success).length}/${results.length}`);
    
    return results;
}