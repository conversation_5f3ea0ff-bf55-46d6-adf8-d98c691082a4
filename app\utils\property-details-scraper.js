// 加载环境变量
require('dotenv').config();

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

// 添加stealth插件
puppeteer.use(StealthPlugin());

// LLM配置 - 可以轻松切换不同的模型提供商
const LLM_CONFIG = {
    // Ollama本地配置
    ollama: {
        baseURL: 'http://localhost:11434/v1',
        apiKey: 'ollama',
        model: 'gemma3:4b'
    },
    // OpenAI配置（备用）
    openai: {
        baseURL: 'https://api.openai.com/v1',
        apiKey: process.env.OPENAI_API_KEY,
        model: 'gpt-5-nano'
    },
    // 其他模型配置可以在这里添加
};

// 当前使用的配置
const currentProvider = 'openai';
const config = LLM_CONFIG[currentProvider];

// 检查 API 密钥是否存在
if (currentProvider === 'openai' && !config.apiKey) {
    console.error('❌ 错误: OPENAI_API_KEY 环境变量未设置');
    console.log('💡 请确保 .env 文件中包含: OPENAI_API_KEY=your_api_key_here');
    process.exit(1);
}

// 配置LLM客户端
const llmClient = new OpenAI({
    baseURL: config.baseURL,
    apiKey: config.apiKey
});

class PropertyDetailScraper {
    constructor() {
        this.browser = null;
        this.results = [];
    }

    async init() {
        console.log('🕵️ 启动隐身模式浏览器...');
        
        this.browser = await puppeteer.launch({
            headless: false, // 保持可见以观察
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ],
            defaultViewport: {
                width: 1920,
                height: 1080
            }
        });
    }

    async createStealthPage() {
        const page = await this.browser.newPage();
        
        // 设置用户代理
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        // 设置额外的头信息
        await page.setExtraHTTPHeaders({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'DNT': '1'
        });
        
        // 设置时区和语言
        await page.emulateTimezone('Australia/Sydney');
        
        return page;
    }

    // 人性化延迟
    async humanDelay(min = 1000, max = 3000) {
        const delay = Math.random() * (max - min) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    // 过滤链接列表，每个网站只保留第一个
    filterPropertyLinks(propertyLinks) {
        const siteSeen = new Set();
        const filteredLinks = [];
        
        for (const link of propertyLinks) {
            const siteName = this.getSiteName(link.url);
            if (!siteSeen.has(siteName)) {
                siteSeen.add(siteName);
                filteredLinks.push(link);
                console.log(`✅ 保留 ${siteName}: ${link.title}`);
            } else {
                console.log(`⏭️ 跳过 ${siteName} (已有链接): ${link.title}`);
            }
        }
        
        return filteredLinks;
    }

    // 异步并发处理房产详情
    async scrapePropertyDetails(propertyLinks = []) {
        if (!propertyLinks || propertyLinks.length === 0) {
            throw new Error('请提供房产链接列表');
        }

        // 过滤链接，每个网站只保留第一个
        const filteredLinks = this.filterPropertyLinks(propertyLinks);
        
        console.log(`\n🔍 开始并发访问 ${filteredLinks.length} 个房产链接...\n`);

        // 设置并发限制，避免过载
        const CONCURRENT_LIMIT = 3;
        const results = [];
        
        // 分批处理
        for (let i = 0; i < filteredLinks.length; i += CONCURRENT_LIMIT) {
            const batch = filteredLinks.slice(i, i + CONCURRENT_LIMIT);
            console.log(`📦 处理批次 ${Math.floor(i / CONCURRENT_LIMIT) + 1}/${Math.ceil(filteredLinks.length / CONCURRENT_LIMIT)}`);
            
            // 并发处理当前批次
            const batchPromises = batch.map(async (property, index) => {
                const globalIndex = i + index;
                console.log(`📍 [${globalIndex + 1}/${filteredLinks.length}] 开始访问: ${property.title}`);
                console.log(`🔗 URL: ${property.url}`);
                
                try {
                    const result = await this.scrapePropertyPage(property);
                    console.log(`✅ [${globalIndex + 1}] 完成: ${property.title}`);
                    return result;
                } catch (error) {
                    console.log(`❌ [${globalIndex + 1}] 失败: ${property.title} - ${error.message}`);
                    return {
                        originalTitle: property.title,
                        originalUrl: property.url,
                        success: false,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            });
            
            // 等待当前批次完成
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            
            // 批次间延迟
            if (i + CONCURRENT_LIMIT < filteredLinks.length) {
                console.log('⏳ 批次间等待...');
                await this.humanDelay(2000, 4000);
            }
        }

        this.results = results;
        return this.results;
    }

    async scrapePropertyPage(property) {
        const page = await this.createStealthPage();
        const startTime = Date.now();
        
        try {
            console.log('  - 正在访问页面...');
            
            // 访问页面
            await page.goto(property.url, { 
                waitUntil: 'domcontentloaded',
                timeout: 30000 
            });
            
            const loadTime = Date.now() - startTime;
            console.log(`  - 页面加载完成 (${loadTime}ms)`);
            
            // 等待页面完全加载
            await this.humanDelay(2000, 4000);
            
            // 获取基本信息
            const pageTitle = await page.title();
            const currentUrl = page.url();
            
            console.log(`  - 页面标题: ${pageTitle}`);
            
            // 检查是否需要处理安全检查
            if (pageTitle.includes('Security Checkpoint') || pageTitle.includes('Vercel') || pageTitle.includes('Access Denied')) {
                console.log('  - 🚨 检测到访问限制，尝试绕过...');
                
                for (let attempt = 0; attempt < 15; attempt++) {
                    console.log(`  - 尝试绕过 ${attempt + 1}/15...`);
                    
                    // 模拟人类行为
                    await page.mouse.move(
                        Math.random() * 1000 + 100,
                        Math.random() * 600 + 100
                    );
                    
                    await this.humanDelay(2000, 4000);
                    
                    // 检查是否通过
                    const newTitle = await page.title();
                    if (!newTitle.includes('Security Checkpoint') && !newTitle.includes('Vercel') && !newTitle.includes('Access Denied')) {
                        console.log('  - ✅ 成功绕过访问限制！');
                        break;
                    }
                    
                    if (attempt === 14) {
                        throw new Error('无法绕过访问限制');
                    }
                }
            }
            
            // 提取房产信息
            console.log('  - 正在提取房产信息...');
            const propertyData = await this.extractPropertyData(page, property.url);
            
            console.log(`  - ✅ 成功提取信息`);
            
            return {
                originalTitle: property.title,
                originalUrl: property.url,
                actualUrl: currentUrl,
                pageTitle: await page.title(),
                success: true,
                loadTime: loadTime,
                extractedData: propertyData,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.log(`  - ❌ 访问失败: ${error.message}`);
            
            // 保存错误页面用于调试
            try {
                const errorFileName = `error-${Date.now()}-${i}`;
                await page.screenshot({ path: `${errorFileName}.png` });
                const html = await page.content();
                fs.writeFileSync(`${errorFileName}.html`, html);
                console.log(`  - 📸 已保存错误截图和HTML: ${errorFileName}`);
            } catch (e) {
                console.log(`  - 无法保存调试文件: ${e.message}`);
            }
            
            return {
                originalTitle: property.title,
                originalUrl: property.url,
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            await page.close();
        }
    }

    async extractPropertyData(page, url) {
        console.log('  - 正在获取页面HTML内容...');
        
        // 获取页面的HTML内容
        const htmlContent = await page.content();
        
        console.log('  - 正在使用LLM分析房产信息...');
        
        try {
            // 使用LLM来解析HTML内容
            const propertyData = await this.extractDataWithLLM(htmlContent, url);
            return propertyData;
            
        } catch (error) {
            console.log(`  - LLM提取数据时出错: ${error.message}`);
            return {
                siteName: this.getSiteName(url),
                error: error.message,
                extractionMethod: 'LLM_FAILED'
            };
        }
    }

    async extractDataWithLLM(htmlContent, url) {
        const siteName = this.getSiteName(url);
        
        // 构建详细的提示词
        const prompt = `你是一个房产信息提取专家。请分析以下HTML内容，提取房产信息。

**重要**: 你必须只返回JSON格式的数据，不要添加任何其他文字说明。

从HTML中提取以下信息，如果找不到就写"N/A"。注意价格范围用"minPrice-maxPrice"格式，历史记录用数组格式。

返回格式示例：
{
  "siteName": "${siteName}",
  "address": "完整地址",
  "propertyType": "房产类型",
  "bedrooms": "卧室数量",
  "bathrooms": "浴室数量", 
  "parking": "停车位数量",
  "landSize": "土地面积",
  "buildingSize": "建筑面积",
  "yearBuilt": "建成年份",
  "currentGuidePrice": "当前guide价格",
  "estimatedValueRange": "估价范围(如: $800,000-$900,000)",
  "estimatedValueMid": "估价中位数",
  "auctionDate": "拍卖日期和时间",
  "inspectionTimes": ["开放检查时间1", "开放检查时间2"],
  "historyRecords": [
    {
      "type": "listing|sale",
      "date": "日期",
      "price": "价格或guide价格",
      "details": "详细信息"
    }
  ],
  "description": "房产简短描述",
  "features": ["特色1", "特色2"],
  "contact": "联系方式",
  "extractionMethod": "LLM"
}

HTML内容：
${htmlContent.substring(0, 35000)}`; // 稍微增加HTML长度以获取更多信息

        try {
            console.log(`  - 使用模型: ${config.model} (${currentProvider})`);
            
            const response = await llmClient.chat.completions.create({
                model: config.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
            });

            const content = response.choices[0].message.content;
            console.log('  - LLM原始响应长度:', content.length);
            
            // 调试：保存原始响应
            if (process.env.DEBUG_LLM) {
                console.log('  - [DEBUG] LLM原始响应:', content.substring(0, 500) + '...');
            }
            
            // 尝试解析JSON响应
            let propertyData;
            
            // 首先尝试找JSON代码块
            const codeBlockMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
            let jsonText = null;
            
            if (codeBlockMatch) {
                jsonText = codeBlockMatch[1];
            } else {
                // 如果没有代码块，尝试找普通的JSON对象
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    jsonText = jsonMatch[0];
                }
            }
            
            if (jsonText) {
                try {
                    propertyData = JSON.parse(jsonText);
                    console.log('  - ✅ LLM成功提取房产信息');
                } catch (parseError) {
                    console.log('  - ⚠️ JSON解析失败，尝试修复...');
                    // 尝试修复常见的JSON问题
                    const fixedJson = jsonText
                        .replace(/,\s*}/g, '}')  // 移除尾随逗号
                        .replace(/,\s*]/g, ']')   // 移除数组尾随逗号
                        .replace(/"\s*:\s*"([^"]*)"([^,}\]]*)/g, '": "$1$2"'); // 修复引号问题
                    
                    try {
                        propertyData = JSON.parse(fixedJson);
                        console.log('  - ✅ JSON修复成功');
                    } catch (secondError) {
                        throw new Error(`JSON解析失败: ${parseError.message}`);
                    }
                }
            } else {
                throw new Error('LLM响应中未找到有效的JSON格式');
            }
            
            if (propertyData) {
                
                // 显示提取的关键信息
                if (propertyData.currentGuidePrice && propertyData.currentGuidePrice !== 'N/A') {
                    console.log(`    💰 当前Guide价格: ${propertyData.currentGuidePrice}`);
                }
                if (propertyData.estimatedValueRange && propertyData.estimatedValueRange !== 'N/A') {
                    console.log(`    📊 估价范围: ${propertyData.estimatedValueRange}`);
                }
                if (propertyData.estimatedValueMid && propertyData.estimatedValueMid !== 'N/A') {
                    console.log(`    📈 估价中位数: ${propertyData.estimatedValueMid}`);
                }
                if (propertyData.address && propertyData.address !== 'N/A') {
                    console.log(`    📍 地址: ${propertyData.address}`);
                }
                if (propertyData.bedrooms && propertyData.bedrooms !== 'N/A') {
                    console.log(`    🏠 房间: ${propertyData.bedrooms}床 ${propertyData.bathrooms || 'N/A'}浴`);
                }
                if (propertyData.auctionDate && propertyData.auctionDate !== 'N/A') {
                    console.log(`    🔨 拍卖时间: ${propertyData.auctionDate}`);
                }
                if (propertyData.yearBuilt && propertyData.yearBuilt !== 'N/A') {
                    console.log(`    🏗️ 建成年份: ${propertyData.yearBuilt}`);
                }
                if (propertyData.historyRecords && Array.isArray(propertyData.historyRecords) && propertyData.historyRecords.length > 0) {
                    console.log(`    📜 历史记录: ${propertyData.historyRecords.length} 条`);
                }
                
                return propertyData;
            } else {
                throw new Error('LLM响应中未找到有效的JSON格式');
            }
            
        } catch (error) {
            console.log(`  - LLM处理错误: ${error.message}`);
            
            // 如果LLM失败，返回基本信息
            return {
                siteName: siteName,
                address: 'N/A',
                propertyType: 'N/A',
                bedrooms: 'N/A',
                bathrooms: 'N/A',
                parking: 'N/A',
                landSize: 'N/A',
                buildingSize: 'N/A',
                yearBuilt: 'N/A',
                currentGuidePrice: 'N/A',
                estimatedValueRange: 'N/A',
                estimatedValueMid: 'N/A',
                auctionDate: 'N/A',
                inspectionTimes: [],
                historyRecords: [],
                description: 'N/A',
                features: [],
                contact: 'N/A',
                extractionMethod: 'LLM_FAILED',
                error: error.message
            };
        }
    }

    getSiteName(url) {
        if (url.includes('domain.com.au')) return 'Domain';
        if (url.includes('realestate.com.au')) return 'RealEstate.com.au';
        if (url.includes('realtor.com')) return 'Realtor.com';
        if (url.includes('view.com.au')) return 'View.com.au';
        if (url.includes('onthehouse.com.au')) return 'OnTheHouse.com.au';
        if (url.includes('propertyvalue.com.au')) return 'PropertyValue.com.au';
        return 'Unknown';
    }

    async saveResults(filename = 'property-details-results.json') {
        const filepath = path.join(__dirname, filename);
        const output = {
            searchDate: new Date().toISOString(),
            totalLinks: this.results.length,
            successCount: this.results.filter(r => r.success).length,
            failureCount: this.results.filter(r => !r.success).length,
            results: this.results
        };
        
        fs.writeFileSync(filepath, JSON.stringify(output, null, 2), 'utf8');
        console.log(`\n💾 详细结果已保存到: ${filepath}`);
        
        // 创建简化的摘要
        const summaryFile = filename.replace('.json', '-summary.json');
        const summary = {
            searchDate: new Date().toISOString(),
            totalLinks: this.results.length,
            successCount: output.successCount,
            failureCount: output.failureCount,
            propertyData: this.results.filter(r => r.success).map(r => ({
                siteName: r.extractedData?.siteName || 'Unknown',
                address: r.extractedData?.address || 'N/A',
                bedrooms: r.extractedData?.bedrooms || 'N/A',
                bathrooms: r.extractedData?.bathrooms || 'N/A',
                yearBuilt: r.extractedData?.yearBuilt || 'N/A',
                currentGuidePrice: r.extractedData?.currentGuidePrice || 'N/A',
                estimatedValueRange: r.extractedData?.estimatedValueRange || 'N/A',
                estimatedValueMid: r.extractedData?.estimatedValueMid || 'N/A',
                auctionDate: r.extractedData?.auctionDate || 'N/A',
                inspectionTimesCount: Array.isArray(r.extractedData?.inspectionTimes) ? r.extractedData.inspectionTimes.length : 0,
                historyRecordsCount: Array.isArray(r.extractedData?.historyRecords) ? r.extractedData.historyRecords.length : 0,
                url: r.originalUrl
            }))
        };
        
        fs.writeFileSync(path.join(__dirname, summaryFile), JSON.stringify(summary, null, 2), 'utf8');
        console.log(`📋 摘要已保存到: ${summaryFile}`);
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// 主要的房产信息抓取函数
    const scraper = new PropertyDetailScraper();
    
    try {
        await scraper.init();
        
        console.log('🏠 17 Campbell Drive, Wahroonga NSW 2076 - 房产详情搜集');
        console.log('🕵️ 使用隐身模式访问多个房产网站');
        console.log(`🤖 使用 ${config.model} (${currentProvider}) 进行智能数据提取\n`);
        
        const results = await scraper.scrapePropertyDetails();
        
        // 显示结果摘要
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        
        console.log('\n📊 搜集完成统计:');
        console.log(`✅ 成功: ${successCount} 个网站`);
        console.log(`❌ 失败: ${failureCount} 个网站`);
        console.log(`📈 成功率: ${((successCount / results.length) * 100).toFixed(1)}%`);
        
        // 显示成功提取的数据摘要
        if (successCount > 0) {
            console.log('\n🏠 房产信息摘要:');
            results.filter(r => r.success).forEach((result, index) => {
                const data = result.extractedData;
                console.log(`\n${index + 1}. ${data.siteName}:`);
                if (data.address !== 'N/A') console.log(`   📍 地址: ${data.address}`);
                if (data.currentGuidePrice !== 'N/A') console.log(`   💰 Guide价格: ${data.currentGuidePrice}`);
                if (data.estimatedValueRange !== 'N/A') console.log(`   � 估价范围: ${data.estimatedValueRange}`);
                if (data.estimatedValueMid !== 'N/A') console.log(`   📈 估价中位数: ${data.estimatedValueMid}`);
                if (data.bedrooms !== 'N/A' || data.bathrooms !== 'N/A') {
                    console.log(`   🏠 房间: ${data.bedrooms}床 ${data.bathrooms}浴`);
                }
                if (data.yearBuilt !== 'N/A') console.log(`   🏗️ 建成年份: ${data.yearBuilt}`);
                if (data.auctionDate !== 'N/A') console.log(`   � 拍卖: ${data.auctionDate}`);
                if (Array.isArray(data.inspectionTimes) && data.inspectionTimes.length > 0) {
                    console.log(`   👀 开放检查: ${data.inspectionTimes.length} 次`);
                }
                if (Array.isArray(data.historyRecords) && data.historyRecords.length > 0) {
                    console.log(`   � 历史记录: ${data.historyRecords.length} 条`);
                }
            });
        }
        
        await scraper.saveResults();
        
        return {
            success: true,
            results: results,
            stats: {
                total: results.length,
                successful: successCount,
                failed: failureCount,
                successRate: ((successCount / results.length) * 100).toFixed(1)
            }
        };
        
    } catch (error) {
        console.error('❌ 脚本运行错误:', error.message);
        return {
            success: false,
            error: error.message,
            results: []
        };
    } finally {
        await scraper.close();
        console.log('\n✅ 房产详情搜集完成!');
    }
}

// 主要的房产信息抓取函数
async function scrapePropertyInfo(propertyLinks, options = {}) {
    const {
        saveResults = true,
        outputFilename = 'property-details-results.json',
        showSummary = true
    } = options;
    
    const scraper = new PropertyDetailScraper();
    
    try {
        await scraper.init();
        
        console.log('🏠 房产详情搜集');
        console.log('🕵️ 使用隐身模式访问多个房产网站');
        console.log(`🤖 使用 ${config.model} (${currentProvider}) 进行智能数据提取\n`);
        
        const results = await scraper.scrapePropertyDetails(propertyLinks);
        
        // 显示结果摘要
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        
        console.log('\n📊 搜集完成统计:');
        console.log(`✅ 成功: ${successCount} 个网站`);
        console.log(`❌ 失败: ${failureCount} 个网站`);
        console.log(`📈 成功率: ${((successCount / results.length) * 100).toFixed(1)}%`);
        
        // 显示成功提取的数据摘要
        if (showSummary && successCount > 0) {
            console.log('\n🏠 房产信息摘要:');
            results.filter(r => r.success).forEach((result, index) => {
                const data = result.extractedData;
                console.log(`\n${index + 1}. ${data.siteName}:`);
                if (data.address !== 'N/A') console.log(`   📍 地址: ${data.address}`);
                if (data.currentGuidePrice !== 'N/A') console.log(`   💰 Guide价格: ${data.currentGuidePrice}`);
                if (data.estimatedValueRange !== 'N/A') console.log(`   📊 估价范围: ${data.estimatedValueRange}`);
                if (data.estimatedValueMid !== 'N/A') console.log(`   📈 估价中位数: ${data.estimatedValueMid}`);
                if (data.bedrooms !== 'N/A' || data.bathrooms !== 'N/A') {
                    console.log(`   🏠 房间: ${data.bedrooms}床 ${data.bathrooms}浴`);
                }
                if (data.yearBuilt !== 'N/A') console.log(`   🏗️ 建成年份: ${data.yearBuilt}`);
                if (data.auctionDate !== 'N/A') console.log(`   🔨 拍卖: ${data.auctionDate}`);
                if (Array.isArray(data.inspectionTimes) && data.inspectionTimes.length > 0) {
                    console.log(`   👀 开放检查: ${data.inspectionTimes.length} 次`);
                }
                if (Array.isArray(data.historyRecords) && data.historyRecords.length > 0) {
                    console.log(`   📜 历史记录: ${data.historyRecords.length} 条`);
                }
            });
        }
        
        if (saveResults) {
            await scraper.saveResults(outputFilename);
        }
        
        return {
            success: true,
            results: results,
            stats: {
                total: results.length,
                successful: successCount,
                failed: failureCount,
                successRate: ((successCount / results.length) * 100).toFixed(1)
            }
        };
        
    } catch (error) {
        console.error('❌ 脚本运行错误:', error.message);
        return {
            success: false,
            error: error.message,
            results: []
        };
    } finally {
        await scraper.close();
        console.log('\n✅ 房产详情搜集完成!');
    }
}

// 示例主函数（用于直接运行测试）
async function mainExample() {
    // 示例链接列表
    const propertyLinks = [
        {
            "title": "17 Campbell Drive, Wahroonga, NSW 2076 - Realtor.com",
            "url": "https://www.realtor.com/international/au/17-campbell-drive-wahroonga-nsw-149024232/"
        },
        {
            "title": "17 Campbell Drive, Wahroonga NSW 2076 - Domain",
            "url": "https://www.domain.com.au/17-campbell-drive-wahroonga-nsw-2076-2020264327"
        },
        {
            "title": "17 Campbell Drive, Wahroonga, NSW 2076 for Auction | view.com.au",
            "url": "https://view.com.au/property/nsw/wahroonga-2076/17-campbell-drive-17053114/"
        },
        {
            "title": "17 Campbell Drive, Wahroonga | Property Value Estimate & History",
            "url": "https://www.domain.com.au/property-profile/17-campbell-drive-wahroonga-nsw-2076"
        },
        {
            "title": "17 Campbell Drive, Wahroonga, NSW 2076 - realestate.com.au",
            "url": "https://www.realestate.com.au/property/17-campbell-dr-wahroonga-nsw-2076"
        },
        {
            "title": "17 Campbell Drive, Wahroonga, NSW 2076 | Property Value ...",
            "url": "https://www.onthehouse.com.au/property/nsw/wahroonga-2076/17-campbell-dr-wahroonga-nsw-2076-11830819"
        },
        {
            "title": "17 Campbell Drive Wahroonga NSW 2076 Sold Prices and Statistics",
            "url": "https://www.propertyvalue.com.au/property/17-campbell-drive-wahroonga-nsw-2076/7187565"
        }
    ];
    
    const result = await scrapePropertyInfo(propertyLinks);
    
    if (result.success) {
        console.log(`\n✨ 抓取完成! 成功率: ${result.stats.successRate}%`);
    } else {
        console.log(`\n❌ 抓取失败: ${result.error}`);
    }
}

// 运行脚本
if (require.main === module) {
    mainExample().catch(console.error);
}

// 导出主要的函数和类
module.exports = {
    PropertyDetailScraper,
    scrapePropertyInfo,
    LLM_CONFIG,
    config
};