// 定义数据类型
export interface PropertyData {
  id: string;
  address: string;
  searchDate: string;
  totalSources: number;
  successfulSources: number;
  properties: PropertyInfo[];
}

export interface PropertyInfo {
  siteName: string;
  address: string;
  propertyType: string;
  bedrooms: string;
  bathrooms: string;
  parking: string;
  landSize: string;
  buildingSize: string;
  yearBuilt: string;
  currentGuidePrice: string;
  estimatedValueRange: string;
  estimatedValueMid: string;
  auctionDate: string;
  inspectionTimes: string[];
  historyRecords: any[];
  description: string;
  features: string[];
  contact: string;
  sourceUrl: string;
}

// 模拟搜索和抓取功能（实际调用将通过API路由处理）
export async function searchAndScrapeProperty(address: string): Promise<{success: boolean, data?: PropertyData, error?: string}> {
  // 这个函数将在客户端调用API路由来执行实际的搜索和抓取
  try {
    const response = await fetch('/api/search-property', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    });
    
    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }
    
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('❌ 搜索失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// 本地存储工具函数
export const localStorageUtils = {
  saveProperties: (properties: any[]) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('propertySearchResults', JSON.stringify(properties));
    }
  },
  
  loadProperties: (): any[] => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('propertySearchResults');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  },
  
  removeProperty: (index: number) => {
    if (typeof window !== 'undefined') {
      const properties = localStorageUtils.loadProperties();
      properties.splice(index, 1);
      localStorageUtils.saveProperties(properties);
      return properties;
    }
    return [];
  }
};

// 剪贴板工具函数
export const clipboardUtils = {
  readText: async (): Promise<string> => {
    if (typeof window !== 'undefined' && navigator.clipboard) {
      try {
        return await navigator.clipboard.readText();
      } catch (error) {
        console.error('无法读取剪贴板:', error);
        throw new Error('无法访问剪贴板，请检查浏览器权限');
      }
    }
    throw new Error('剪贴板API不可用');
  }
};