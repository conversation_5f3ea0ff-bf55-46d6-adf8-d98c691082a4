// 加载环境变量
require('dotenv').config();

const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');

// LLM配置 - 可以轻松切换不同的模型提供商
const LLM_CONFIG = {
    // Ollama本地配置
    ollama: {
        baseURL: 'http://localhost:11434/v1',
        apiKey: 'ollama',
        model: 'gemma3:4b'
    },
    // OpenAI配置（备用）
    openai: {
        baseURL: 'https://api.openai.com/v1',
        apiKey: process.env.OPENAI_API_KEY,
        model: 'gpt-4o-mini'
    },
    // 其他模型配置可以在这里添加
};

// 当前使用的配置
const currentProvider = 'openai';
const config = LLM_CONFIG[currentProvider];

// 检查 API 密钥是否存在
if (currentProvider === 'openai' && !config.apiKey) {
    console.error('❌ 错误: OPENAI_API_KEY 环境变量未设置');
    console.log('💡 请确保 .env 文件中包含: OPENAI_API_KEY=your_api_key_here');
    process.exit(1);
}

// 配置LLM客户端
const llmClient = new OpenAI({
    baseURL: config.baseURL,
    apiKey: config.apiKey
});

/**
 * 使用LLM汇总房产详情信息
 * @param {Array<Object>} propertyDetailsList - 从property details scraper得到的信息列表
 * @param {Object} options - 汇总选项
 * @param {string} options.propertyAddress - 房产地址
 * @param {string} options.outputFormat - 输出格式: 'json' | 'markdown' | 'text'，默认'json'
 * @param {boolean} options.includeRawData - 是否包含原始数据，默认false
 * @param {boolean} options.saveToFile - 是否保存到文件，默认false
 * @param {string} options.outputFilename - 输出文件名
 * @returns {Promise<Object>} 汇总结果
 */
async function summarizePropertyInfo(propertyDetailsList, options = {}) {
    const {
        propertyAddress = 'Unknown Property',
        outputFormat = 'json',
        includeRawData = false,
        saveToFile = false,
        outputFilename = null
    } = options;

    console.log(`📊 开始汇总房产信息: ${propertyAddress}`);
    console.log(`🏢 数据源数量: ${propertyDetailsList.length}`);
    console.log(`🤖 使用模型: ${config.model} (${currentProvider})`);

    try {
        // 过滤有效的数据
        const validData = propertyDetailsList.filter(item => 
            item.success && 
            item.extractedData && 
            item.extractedData.extractionMethod !== 'LLM_FAILED'
        );

        console.log(`✅ 有效数据源: ${validData.length}/${propertyDetailsList.length}`);

        if (validData.length === 0) {
            throw new Error('没有有效的房产数据可供汇总');
        }

        // 准备数据摘要用于送入LLM
        const dataSummary = validData.map(item => ({
            siteName: item.extractedData.siteName,
            address: item.extractedData.address,
            propertyType: item.extractedData.propertyType,
            bedrooms: item.extractedData.bedrooms,
            bathrooms: item.extractedData.bathrooms,
            parking: item.extractedData.parking,
            landSize: item.extractedData.landSize,
            buildingSize: item.extractedData.buildingSize,
            yearBuilt: item.extractedData.yearBuilt,
            currentGuidePrice: item.extractedData.currentGuidePrice,
            estimatedValueRange: item.extractedData.estimatedValueRange,
            estimatedValueMid: item.extractedData.estimatedValueMid,
            auctionDate: item.extractedData.auctionDate,
            inspectionTimes: item.extractedData.inspectionTimes || [],
            historyRecords: item.extractedData.historyRecords || [],
            description: item.extractedData.description,
            features: item.extractedData.features || [],
            contact: item.extractedData.contact,
            sourceUrl: item.originalUrl
        }));

        // 构建LLM提示词
        const prompt = buildSummaryPrompt(dataSummary, propertyAddress, outputFormat);

        console.log('⏳ 正在使用LLM分析和汇总数据...');
        const startTime = Date.now();

        // 调用LLM
        const response = await llmClient.chat.completions.create({
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: '你是一个专业的房产分析师，擅长汇总和分析房产信息，提供准确、全面的房产报告。'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.3, // 降低创造性，提高准确性
            max_tokens: 4000
        });

        const processingTime = Date.now() - startTime;
        console.log(`✅ LLM汇总完成 (${processingTime}ms)`);

        const summaryContent = response.choices[0].message.content;
        
        // 解析结果
        let parsedSummary;
        if (outputFormat === 'json') {
            try {
                // 尝试提取JSON
                const jsonMatch = summaryContent.match(/```json\s*([\s\S]*?)\s*```/) || 
                                 summaryContent.match(/\{[\s\S]*\}/);
                
                if (jsonMatch) {
                    const jsonText = jsonMatch[1] || jsonMatch[0];
                    parsedSummary = JSON.parse(jsonText);
                } else {
                    throw new Error('未找到有效的JSON格式');
                }
            } catch (parseError) {
                console.log('⚠️ JSON解析失败，返回原始文本');
                parsedSummary = {
                    error: 'JSON_PARSE_FAILED',
                    rawContent: summaryContent
                };
            }
        } else {
            parsedSummary = summaryContent;
        }

        // 构建完整结果
        const result = {
            success: true,
            propertyAddress: propertyAddress,
            summaryFormat: outputFormat,
            summary: parsedSummary,
            metadata: {
                totalDataSources: propertyDetailsList.length,
                validDataSources: validData.length,
                sourceSites: [...new Set(validData.map(item => item.extractedData.siteName))],
                processingTime: processingTime,
                model: config.model,
                provider: currentProvider,
                timestamp: new Date().toISOString()
            }
        };

        // 包含原始数据（如果需要）
        if (includeRawData) {
            result.rawData = dataSummary;
        }

        // 保存到文件（如果需要）
        if (saveToFile) {
            const filename = outputFilename || `property-summary-${Date.now()}.json`;
            const filepath = path.join(__dirname, filename);
            fs.writeFileSync(filepath, JSON.stringify(result, null, 2), 'utf8');
            console.log(`💾 汇总结果已保存到: ${filepath}`);
            result.savedTo = filepath;
        }

        // 显示汇总信息
        displaySummaryOverview(result);

        return result;

    } catch (error) {
        console.error('❌ 房产信息汇总失败:', error.message);
        
        return {
            success: false,
            error: error.message,
            propertyAddress: propertyAddress,
            metadata: {
                totalDataSources: propertyDetailsList.length,
                timestamp: new Date().toISOString(),
                errorDetails: error.stack
            }
        };
    }
}

/**
 * 构建LLM汇总提示词
 * @param {Array} dataSummary - 数据摘要
 * @param {string} propertyAddress - 房产地址
 * @param {string} outputFormat - 输出格式
 * @returns {string} 构建的提示词
 */
function buildSummaryPrompt(dataSummary, propertyAddress, outputFormat) {
    const promptIntro = `请分析以下来自多个房产网站的数据，为房产"${propertyAddress}"生成一个全面的汇总报告。

数据来源包括: ${[...new Set(dataSummary.map(d => d.siteName))].join(', ')}

请注意：
1. 合并和验证来自不同来源的信息
2. 识别数据中的一致性和差异
3. 提供最可能准确的信息
4. 标注数据来源和可信度
5. 突出重要的房产特征和价格信息

原始数据：
${JSON.stringify(dataSummary, null, 2)}`;

    let formatInstructions = '';
    
    if (outputFormat === 'json') {
        formatInstructions = `
请以JSON格式返回汇总结果，包含以下结构：

\`\`\`json
{
  "propertyOverview": {
    "address": "统一的完整地址",
    "propertyType": "房产类型",
    "bedrooms": "卧室数量",
    "bathrooms": "浴室数量",
    "parking": "停车位数量",
    "landSize": "土地面积",
    "buildingSize": "建筑面积",
    "yearBuilt": "建成年份"
  },
  "priceAnalysis": {
    "currentGuidePrice": "当前guide价格",
    "estimatedValueRange": "估价范围",
    "estimatedValueMid": "估价中位数",
    "priceDataSources": ["价格信息来源"],
    "priceConsistency": "价格数据一致性评价"
  },
  "marketInfo": {
    "auctionDate": "拍卖日期",
    "inspectionTimes": ["开放检查时间列表"],
    "marketStatus": "市场状态分析"
  },
  "propertyHistory": {
    "consolidatedHistory": [
      {
        "type": "listing|sale",
        "date": "日期",
        "price": "价格",
        "details": "详情",
        "source": "数据来源"
      }
    ],
    "historySummary": "历史记录汇总分析"
  },
  "propertyFeatures": {
    "consolidatedFeatures": ["合并后的特色列表"],
    "keyHighlights": ["重点特色"],
    "description": "综合描述"
  },
  "dataQuality": {
    "sourcesCount": 数据源数量,
    "dataConsistency": "数据一致性评价",
    "reliabilityScore": "可靠性评分(1-10)",
    "dataConflicts": ["发现的数据冲突"],
    "recommendations": ["数据使用建议"]
  },
  "contactInfo": {
    "agents": ["联系人信息"],
    "sources": ["联系方式来源"]
  }
}
\`\`\``;
    } else if (outputFormat === 'markdown') {
        formatInstructions = `
请以Markdown格式返回汇总结果，包含以下章节：

# 房产汇总报告: ${propertyAddress}

## 📍 房产概览
## 💰 价格分析  
## 🏠 房产特征
## 📅 市场信息
## 📜 历史记录
## 📊 数据质量评估
## 📞 联系信息

确保使用适当的Markdown格式，包括表格、列表和强调。`;
    } else {
        formatInstructions = `
请以清晰的文本格式返回汇总结果，包含：
- 房产基本信息
- 价格分析
- 重要特征
- 市场状况
- 数据质量评估`;
    }

    return promptIntro + formatInstructions;
}

/**
 * 显示汇总结果概览
 * @param {Object} result - 汇总结果
 */
function displaySummaryOverview(result) {
    console.log('\n📋 房产信息汇总完成:');
    console.log(`🏠 房产地址: ${result.propertyAddress}`);
    console.log(`📊 数据源: ${result.metadata.validDataSources}/${result.metadata.totalDataSources}`);
    console.log(`🌐 来源网站: ${result.metadata.sourceSites.join(', ')}`);
    console.log(`⏱️ 处理时间: ${result.metadata.processingTime}ms`);
    console.log(`🤖 使用模型: ${result.metadata.model}`);
    
    if (result.summary && typeof result.summary === 'object' && result.summary.propertyOverview) {
        const overview = result.summary.propertyOverview;
        console.log('\n🏡 房产概览:');
        if (overview.address && overview.address !== 'N/A') {
            console.log(`  📍 地址: ${overview.address}`);
        }
        if (overview.propertyType && overview.propertyType !== 'N/A') {
            console.log(`  🏠 类型: ${overview.propertyType}`);
        }
        if (overview.bedrooms && overview.bedrooms !== 'N/A') {
            console.log(`  🛏️ 房间: ${overview.bedrooms}床 ${overview.bathrooms || 'N/A'}浴`);
        }
        if (overview.yearBuilt && overview.yearBuilt !== 'N/A') {
            console.log(`  🏗️ 建成: ${overview.yearBuilt}`);
        }
    }
}

/**
 * 批量汇总多个房产的信息
 * @param {Array<Array<Object>>} propertiesDetailsList - 多个房产的详情列表
 * @param {Array<string>} propertyAddresses - 对应的房产地址列表
 * @param {Object} options - 汇总选项
 * @returns {Promise<Array<Object>>} 汇总结果列表
 */
async function summarizeMultipleProperties(propertiesDetailsList, propertyAddresses, options = {}) {
    console.log(`🏘️ 批量汇总 ${propertiesDetailsList.length} 个房产信息`);
    
    const results = [];
    
    for (let i = 0; i < propertiesDetailsList.length; i++) {
        const propertyDetails = propertiesDetailsList[i];
        const address = propertyAddresses[i] || `Property ${i + 1}`;
        
        console.log(`\n📍 [${i + 1}/${propertiesDetailsList.length}] 汇总: ${address}`);
        
        try {
            const result = await summarizePropertyInfo(propertyDetails, {
                ...options,
                propertyAddress: address
            });
            results.push(result);
            
            // 添加延迟避免API限制
            if (i < propertiesDetailsList.length - 1) {
                console.log('⏳ 等待中...');
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        } catch (error) {
            console.error(`❌ 汇总失败 [${address}]:`, error.message);
            results.push({
                success: false,
                error: error.message,
                propertyAddress: address
            });
        }
    }
    
    console.log(`\n✅ 批量汇总完成！成功: ${results.filter(r => r.success).length}/${results.length}`);
    
    return results;
}

module.exports = {
    summarizePropertyInfo,
    summarizeMultipleProperties,
    LLM_CONFIG,
    config
};