// 简化的服务端搜索 - 直接集成所有功能
import Exa from "exa-js";

// 检查必需的环境变量
function checkEnvironmentVariables() {
  const requiredVars = ['EXA_API_KEY'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}。请检查 .env 文件是否正确配置。`);
  }
}

// EXA搜索实现
async function searchPropertyInfo(address: string): Promise<any[]> {
  try {
    console.log(`🔍 EXA搜索: ${address}`);
    
    const exa = new Exa(process.env.EXA_API_KEY);
    
    const searchQuery = `${address} property details real estate listing Australia`;
    console.log(`📝 搜索查询: ${searchQuery}`);
    
    const result = await exa.search(searchQuery, {
      type: "neural",
      useAutoprompt: true,
      numResults: 5,
      includeDomains: [
        'domain.com.au',
        'realestate.com.au', 
        'realtor.com',
        'view.com.au',
        'onthehouse.com.au',
        'propertyvalue.com.au'
      ]
    });
    
    console.log(`✅ EXA搜索完成，找到 ${result.results.length} 个结果`);
    return result.results;
  } catch (error) {
    console.error('❌ EXA搜索失败:', error);
    throw error;
  }
}

// 简化的房产信息处理
function processPropertyData(searchResults: any[], searchAddress: string): any[] {
  return searchResults.map((result, index) => {
    // 从文本内容中提取基本信息
    const text = result.text || '';
    const title = result.title || '';
    
    // 简单的信息提取（正则表达式匹配）
    const bedroomsMatch = text.match(/(\d+)\s*(bed|bedroom|br)/i);
    const bathroomsMatch = text.match(/(\d+)\s*(bath|bathroom|ba)/i);
    const priceMatch = text.match(/\$[\d,]+/g);
    
    return {
      id: index + 1,
      title: title,
      url: result.url,
      siteName: new URL(result.url).hostname,
      address: title.includes(searchAddress) ? searchAddress : 'Address from search',
      bedrooms: bedroomsMatch ? bedroomsMatch[1] : 'N/A',
      bathrooms: bathroomsMatch ? bathroomsMatch[1] : 'N/A',
      priceInfo: priceMatch ? priceMatch[0] : 'N/A',
      description: text.substring(0, 300) + '...',
      sourceUrl: result.url,
      extractedAt: new Date().toISOString()
    };
  });
}

// 执行简化的搜索工作流
export async function executeSearchWorkflow(address: string) {
  try {
    console.log(`🔍 服务端: 开始处理房产地址: ${address}`);
    
    // 检查环境变量
    checkEnvironmentVariables();
    
    // 第1步：EXA搜索
    console.log('📡 服务端: 第1步：EXA搜索...');
    const searchResults = await searchPropertyInfo(address);
    
    if (!searchResults || searchResults.length === 0) {
      throw new Error('EXA搜索未找到相关结果');
    }
    
    // 第2步：处理搜索结果
    console.log('📊 服务端: 第2步：处理搜索结果...');
    const processedData = processPropertyData(searchResults, address);
    
    // 构建最终数据结构
    const summaryData = {
      id: `property-${Date.now()}`,
      address: address,
      searchDate: new Date().toISOString(),
      totalResults: searchResults.length,
      properties: processedData,
      metadata: {
        searchQuery: `${address} property details real estate listing Australia`,
        domains: ['domain.com.au', 'realestate.com.au', 'realtor.com', 'view.com.au'],
        processedAt: new Date().toISOString()
      }
    };
    
    console.log('✅ 服务端: 工作流程完成');
    return {
      success: true,
      data: summaryData
    };
    
  } catch (error) {
    console.error('❌ 服务端: 工作流程失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}