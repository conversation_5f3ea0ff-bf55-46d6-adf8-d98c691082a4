// 服务端工具函数 - 直接导入TypeScript模块
import { searchPropertyInfo } from './exa-search';
import { createRequire } from 'module';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

// 导入CommonJS模块的辅助函数
export async function getCommonJSModules() {
  try {
    // 使用相对路径导入同目录下的CommonJS模块
    const currentDir = path.dirname(__filename);
    
    // 导入房产详情抓取模块
    const scraperPath = path.join(currentDir, 'property-details-scraper.js');
    const scraperModule = require(scraperPath);
    const { PropertyDetailScraper } = scraperModule;
    
    // 导入汇总模块
    const summarizerPath = path.join(currentDir, 'property-summarizer.js');
    const summarizerModule = require(summarizerPath);
    const { summarizePropertyInfo } = summarizerModule;
    
    return { PropertyDetailScraper, summarizePropertyInfo };
  } catch (error) {
    console.error('Failed to import CommonJS modules:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : String(error);
    console.error('Error details:', errorStack);
    throw new Error(`无法加载CommonJS模块: ${errorMessage}`);
  }
}

// 检查必需的环境变量
function checkEnvironmentVariables() {
  const requiredVars = ['EXA_API_KEY', 'OPENAI_API_KEY'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}。请检查 .env 文件是否正确配置。`);
  }
}

// 执行完整的搜索工作流
export async function executeSearchWorkflow(address: string) {
  try {
    console.log(`🔍 服务端: 开始处理房产地址: ${address}`);
    
    // 检查环境变量
    checkEnvironmentVariables();
    
    const { PropertyDetailScraper, summarizePropertyInfo } = await getCommonJSModules();
    
    // 第1步：使用EXA搜索房产信息
    console.log('📡 服务端: 第1步：EXA搜索...');
    const searchResult = await searchPropertyInfo(address, {
      numResults: 5,
      useAutoprompt: true,
      type: 'neural',
      includeDomains: [
        'domain.com.au',
        'realestate.com.au', 
        'realtor.com',
        'view.com.au',
        'onthehouse.com.au',
        'propertyvalue.com.au'
      ],
      excludeDomains: [],
      startCrawlDate: null,
      endCrawlDate: null,
      includeText: true
    });
    
    if (!searchResult.success || !searchResult.results || searchResult.results.length === 0) {
      throw new Error('EXA搜索未找到相关结果');
    }
    
    console.log(`✅ 服务端: EXA搜索完成，找到 ${searchResult.results.length} 个链接`);
    
    // 第2步：使用PropertyDetailScraper抓取详细信息
    console.log('🕷️ 服务端: 第2步：抓取房产详情...');
    const scraper = new PropertyDetailScraper();
    await scraper.init();
    
    try {
      // 转换搜索结果为scraper需要的格式
      const propertyLinks = searchResult.results.map((result: any) => ({
        title: result.title,
        url: result.url
      }));
      
      const scrapeResults = await scraper.scrapePropertyDetails(propertyLinks);
      console.log(`✅ 服务端: 抓取完成，成功处理 ${scrapeResults.filter((r: any) => r.success).length} 个网站`);
      
      // 第3步：使用LLM汇总信息
      console.log('📊 服务端: 第3步：使用LLM汇总信息...');
      const successfulResults = scrapeResults.filter((r: any) => r.success);
      
      if (successfulResults.length === 0) {
        throw new Error('未能从任何网站成功提取房产信息');
      }
      
      // 使用汇总模块处理数据
      let summaryResult;
      try {
        summaryResult = await summarizePropertyInfo(successfulResults, {
          propertyAddress: address,
          outputFormat: 'json',
          includeRawData: false,
          saveToFile: false
        });
        console.log('✅ 服务端: LLM汇总完成');
      } catch (summaryError) {
        console.warn('⚠️ 服务端: LLM汇总失败，使用基础汇总:', summaryError);
        summaryResult = null;
      }
      
      // 构建基础汇总数据（作为备选方案）
      const basicSummaryData = successfulResults.map((result: any) => {
        const data = result.extractedData;
        return {
          siteName: data?.siteName || 'Unknown',
          address: data?.address || 'N/A',
          propertyType: data?.propertyType || 'N/A',
          bedrooms: data?.bedrooms || 'N/A',
          bathrooms: data?.bathrooms || 'N/A',
          parking: data?.parking || 'N/A',
          landSize: data?.landSize || 'N/A',
          buildingSize: data?.buildingSize || 'N/A',
          yearBuilt: data?.yearBuilt || 'N/A',
          currentGuidePrice: data?.currentGuidePrice || 'N/A',
          estimatedValueRange: data?.estimatedValueRange || 'N/A',
          estimatedValueMid: data?.estimatedValueMid || 'N/A',
          auctionDate: data?.auctionDate || 'N/A',
          inspectionTimes: data?.inspectionTimes || [],
          historyRecords: data?.historyRecords || [],
          description: data?.description || 'N/A',
          features: data?.features || [],
          contact: data?.contact || 'N/A',
          sourceUrl: result.originalUrl
        };
      });
      
      // 构建最终数据结构
      const summaryData = {
        id: `property-${Date.now()}`,
        address: address,
        searchDate: new Date().toISOString(),
        totalSources: scrapeResults.length,
        successfulSources: successfulResults.length,
        properties: basicSummaryData,
        llmSummary: summaryResult?.success ? summaryResult.summary : null,
        summaryMetadata: summaryResult?.metadata || null
      };
      
      console.log('✅ 服务端: 工作流程完成');
      return {
        success: true,
        data: summaryData
      };
      
    } finally {
      await scraper.close();
    }
    
  } catch (error) {
    console.error('❌ 服务端: 工作流程失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}