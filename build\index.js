var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: !0 });
};

// app/entry.server.tsx
var entry_server_exports = {};
__export(entry_server_exports, {
  default: () => handleRequest
});
import { RemixServer } from "@remix-run/react";
import { renderToString } from "react-dom/server";
import { jsxDEV } from "react/jsx-dev-runtime";
function handleRequest(request, responseStatusCode, responseHeaders, remixContext) {
  let markup = renderToString(
    /* @__PURE__ */ jsxDEV(RemixServer, { context: remixContext, url: request.url }, void 0, !1, {
      fileName: "app/entry.server.tsx",
      lineNumber: 12,
      columnNumber: 5
    }, this)
  );
  return responseHeaders.set("Content-Type", "text/html"), new Response("<!DOCTYPE html>" + markup, {
    status: responseStatusCode,
    headers: responseHeaders
  });
}

// app/root.tsx
var root_exports = {};
__export(root_exports, {
  default: () => App
});
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration
} from "@remix-run/react";
import { jsxDEV as jsxDEV2 } from "react/jsx-dev-runtime";
function App() {
  return /* @__PURE__ */ jsxDEV2("html", { lang: "zh-CN", children: [
    /* @__PURE__ */ jsxDEV2("head", { children: [
      /* @__PURE__ */ jsxDEV2("meta", { charSet: "utf-8" }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 14,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2("meta", { name: "viewport", content: "width=device-width, initial-scale=1" }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 15,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Meta, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 16,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Links, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 17,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2("style", { children: `
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
          }
          
          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          
          @media (max-width: 768px) {
            .container {
              padding: 10px;
            }
          }
        ` }, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 18,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/root.tsx",
      lineNumber: 13,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV2("body", { children: [
      /* @__PURE__ */ jsxDEV2(Outlet, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 45,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(ScrollRestoration, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 46,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(Scripts, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 47,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV2(LiveReload, {}, void 0, !1, {
        fileName: "app/root.tsx",
        lineNumber: 48,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/root.tsx",
      lineNumber: 44,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/root.tsx",
    lineNumber: 12,
    columnNumber: 5
  }, this);
}

// app/routes/api.search-property.ts
var api_search_property_exports = {};
__export(api_search_property_exports, {
  action: () => action
});
import { json } from "@remix-run/node";

// app/utils/server-search-simple.ts
import Exa from "exa-js";
function checkEnvironmentVariables() {
  let missing = ["EXA_API_KEY"].filter((varName) => !process.env[varName]);
  if (missing.length > 0)
    throw new Error(`\u7F3A\u5C11\u5FC5\u9700\u7684\u73AF\u5883\u53D8\u91CF: ${missing.join(", ")}\u3002\u8BF7\u68C0\u67E5 .env \u6587\u4EF6\u662F\u5426\u6B63\u786E\u914D\u7F6E\u3002`);
}
async function searchPropertyInfo(address) {
  try {
    console.log(`\u{1F50D} EXA\u641C\u7D22: ${address}`);
    let exa = new Exa(process.env.EXA_API_KEY), searchQuery = `${address} property details real estate listing Australia`;
    console.log(`\u{1F4DD} \u641C\u7D22\u67E5\u8BE2: ${searchQuery}`);
    let result = await exa.search(searchQuery, {
      type: "neural",
      useAutoprompt: !0,
      numResults: 5,
      includeDomains: [
        "domain.com.au",
        "realestate.com.au",
        "realtor.com",
        "view.com.au",
        "onthehouse.com.au",
        "propertyvalue.com.au"
      ]
    });
    return console.log(`\u2705 EXA\u641C\u7D22\u5B8C\u6210\uFF0C\u627E\u5230 ${result.results.length} \u4E2A\u7ED3\u679C`), result.results;
  } catch (error) {
    throw console.error("\u274C EXA\u641C\u7D22\u5931\u8D25:", error), error;
  }
}
function processPropertyData(searchResults, searchAddress) {
  return searchResults.map((result, index) => {
    let text = result.text || "", title = result.title || "", bedroomsMatch = text.match(/(\d+)\s*(bed|bedroom|br)/i), bathroomsMatch = text.match(/(\d+)\s*(bath|bathroom|ba)/i), priceMatch = text.match(/\$[\d,]+/g);
    return {
      id: index + 1,
      title,
      url: result.url,
      siteName: new URL(result.url).hostname,
      address: title.includes(searchAddress) ? searchAddress : "Address from search",
      bedrooms: bedroomsMatch ? bedroomsMatch[1] : "N/A",
      bathrooms: bathroomsMatch ? bathroomsMatch[1] : "N/A",
      priceInfo: priceMatch ? priceMatch[0] : "N/A",
      description: text.substring(0, 300) + "...",
      sourceUrl: result.url,
      extractedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
  });
}
async function executeSearchWorkflow(address) {
  try {
    console.log(`\u{1F50D} \u670D\u52A1\u7AEF: \u5F00\u59CB\u5904\u7406\u623F\u4EA7\u5730\u5740: ${address}`), checkEnvironmentVariables(), console.log("\u{1F4E1} \u670D\u52A1\u7AEF: \u7B2C1\u6B65\uFF1AEXA\u641C\u7D22...");
    let searchResults = await searchPropertyInfo(address);
    if (!searchResults || searchResults.length === 0)
      throw new Error("EXA\u641C\u7D22\u672A\u627E\u5230\u76F8\u5173\u7ED3\u679C");
    console.log("\u{1F4CA} \u670D\u52A1\u7AEF: \u7B2C2\u6B65\uFF1A\u5904\u7406\u641C\u7D22\u7ED3\u679C...");
    let processedData = processPropertyData(searchResults, address), summaryData = {
      id: `property-${Date.now()}`,
      address,
      searchDate: (/* @__PURE__ */ new Date()).toISOString(),
      totalResults: searchResults.length,
      properties: processedData,
      metadata: {
        searchQuery: `${address} property details real estate listing Australia`,
        domains: ["domain.com.au", "realestate.com.au", "realtor.com", "view.com.au"],
        processedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
    return console.log("\u2705 \u670D\u52A1\u7AEF: \u5DE5\u4F5C\u6D41\u7A0B\u5B8C\u6210"), {
      success: !0,
      data: summaryData
    };
  } catch (error) {
    return console.error("\u274C \u670D\u52A1\u7AEF: \u5DE5\u4F5C\u6D41\u7A0B\u5931\u8D25:", error), {
      success: !1,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// app/routes/api.search-property.ts
async function action({ request }) {
  if (request.method !== "POST")
    return json({ error: "Method not allowed" }, { status: 405 });
  try {
    let { address } = await request.json();
    if (!address || typeof address != "string")
      return json({ error: "Invalid address provided" }, { status: 400 });
    console.log(`\u{1F50D} API: \u5F00\u59CB\u5904\u7406\u623F\u4EA7\u5730\u5740: ${address}`);
    let result = await executeSearchWorkflow(address);
    return result.success ? json(result) : json(result, { status: 500 });
  } catch (error) {
    return console.error("\u274C API: \u5DE5\u4F5C\u6D41\u7A0B\u5931\u8D25:", error), json({
      success: !1,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// app/routes/_index-old.tsx
var index_old_exports = {};
__export(index_old_exports, {
  default: () => Index
});
import { useState, useEffect } from "react";

// app/utils/property-search.ts
var localStorageUtils = {
  saveProperties: (properties) => {
    typeof window < "u" && localStorage.setItem("propertySearchResults", JSON.stringify(properties));
  },
  loadProperties: () => {
    if (typeof window < "u") {
      let saved = localStorage.getItem("propertySearchResults");
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  },
  removeProperty: (index) => {
    if (typeof window < "u") {
      let properties = localStorageUtils.loadProperties();
      return properties.splice(index, 1), localStorageUtils.saveProperties(properties), properties;
    }
    return [];
  }
}, clipboardUtils = {
  readText: async () => {
    if (typeof window < "u" && navigator.clipboard)
      try {
        return await navigator.clipboard.readText();
      } catch (error) {
        throw console.error("\u65E0\u6CD5\u8BFB\u53D6\u526A\u8D34\u677F:", error), new Error("\u65E0\u6CD5\u8BBF\u95EE\u526A\u8D34\u677F\uFF0C\u8BF7\u68C0\u67E5\u6D4F\u89C8\u5668\u6743\u9650");
      }
    throw new Error("\u526A\u8D34\u677FAPI\u4E0D\u53EF\u7528");
  }
};

// app/routes/_index-old.tsx
import { jsxDEV as jsxDEV3 } from "react/jsx-dev-runtime";
function Index() {
  let [properties, setProperties] = useState([]), [isLoading, setIsLoading] = useState(!1), [error, setError] = useState(null);
  useEffect(() => {
    let savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []), useEffect(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);
  let handleSearchClick = async () => {
    setIsLoading(!0), setError(null);
    try {
      let clipboardText = await clipboardUtils.readText();
      if (!clipboardText.trim())
        throw new Error("\u526A\u8D34\u677F\u5185\u5BB9\u4E3A\u7A7A");
      console.log("\u526A\u8D34\u677F\u5185\u5BB9:", clipboardText);
      let response = await fetch("/api/search-property", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ address: clipboardText.trim() })
      });
      if (!response.ok) {
        let errorData = await response.json();
        throw new Error(errorData.error || `API\u8C03\u7528\u5931\u8D25: ${response.status}`);
      }
      let result = await response.json();
      if (result.success && result.data)
        setProperties((prev) => [result.data, ...prev]);
      else
        throw new Error(result.error || "\u641C\u7D22\u5931\u8D25");
    } catch (error2) {
      console.error("\u641C\u7D22\u5931\u8D25:", error2), setError(error2 instanceof Error ? error2.message : String(error2));
    } finally {
      setIsLoading(!1);
    }
  }, handleDeleteProperty = (index) => {
    setProperties((prev) => prev.filter((_, i) => i !== index));
  };
  return /* @__PURE__ */ jsxDEV3("div", { className: "container", children: [
    /* @__PURE__ */ jsxDEV3("div", { className: "header", children: [
      /* @__PURE__ */ jsxDEV3("h1", { children: "\u{1F3E0} \u623F\u4EA7\u4FE1\u606F\u641C\u7D22" }, void 0, !1, {
        fileName: "app/routes/_index-old.tsx",
        lineNumber: 72,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV3("p", { children: "\u70B9\u51FB\u6309\u94AE\u4ECE\u526A\u8D34\u677F\u8BFB\u53D6\u5730\u5740\u5E76\u641C\u7D22\u623F\u4EA7\u4FE1\u606F" }, void 0, !1, {
        fileName: "app/routes/_index-old.tsx",
        lineNumber: 73,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index-old.tsx",
      lineNumber: 71,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV3("div", { className: "search-section", children: [
      /* @__PURE__ */ jsxDEV3(
        "button",
        {
          className: `search-button ${isLoading ? "loading" : ""}`,
          onClick: handleSearchClick,
          disabled: isLoading,
          children: isLoading ? "\u{1F50D} \u641C\u7D22\u4E2D..." : "\u{1F4CB} \u4ECE\u526A\u8D34\u677F\u641C\u7D22"
        },
        void 0,
        !1,
        {
          fileName: "app/routes/_index-old.tsx",
          lineNumber: 77,
          columnNumber: 9
        },
        this
      ),
      error && /* @__PURE__ */ jsxDEV3("div", { className: "error-message", children: [
        "\u274C ",
        error
      ] }, void 0, !0, {
        fileName: "app/routes/_index-old.tsx",
        lineNumber: 86,
        columnNumber: 11
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index-old.tsx",
      lineNumber: 76,
      columnNumber: 7
    }, this),
    properties.length > 0 && /* @__PURE__ */ jsxDEV3("div", { className: "results-section", children: [
      /* @__PURE__ */ jsxDEV3("h2", { children: [
        "\u641C\u7D22\u7ED3\u679C (",
        properties.length,
        ")"
      ] }, void 0, !0, {
        fileName: "app/routes/_index-old.tsx",
        lineNumber: 94,
        columnNumber: 11
      }, this),
      properties.map((property, index) => /* @__PURE__ */ jsxDEV3("div", { className: "property-card", children: [
        /* @__PURE__ */ jsxDEV3("div", { className: "property-header", children: [
          /* @__PURE__ */ jsxDEV3("h3", { children: property.address }, void 0, !1, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 99,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV3("div", { className: "property-meta", children: [
            /* @__PURE__ */ jsxDEV3("span", { children: [
              "\u{1F5D3}\uFE0F ",
              new Date(property.searchDate).toLocaleString("zh-CN")
            ] }, void 0, !0, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 101,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV3("span", { children: [
              "\u{1F4CA} ",
              property.successfulSources,
              "/",
              property.totalSources,
              " \u6570\u636E\u6E90"
            ] }, void 0, !0, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 102,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV3(
              "button",
              {
                className: "delete-button",
                onClick: () => handleDeleteProperty(index),
                title: "\u5220\u9664\u8FD9\u6761\u8BB0\u5F55",
                children: "\u{1F5D1}\uFE0F"
              },
              void 0,
              !1,
              {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 103,
                columnNumber: 19
              },
              this
            )
          ] }, void 0, !0, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 100,
            columnNumber: 17
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index-old.tsx",
          lineNumber: 98,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV3("div", { className: "property-table-container", children: /* @__PURE__ */ jsxDEV3("table", { className: "property-table", children: [
          /* @__PURE__ */ jsxDEV3("thead", { children: /* @__PURE__ */ jsxDEV3("tr", { children: [
            /* @__PURE__ */ jsxDEV3("th", { children: "\u7F51\u7AD9" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 117,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u623F\u578B" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 118,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u5367\u5BA4" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 119,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u6D74\u5BA4" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 120,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u505C\u8F66" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 121,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u5EFA\u6210\u5E74\u4EFD" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 122,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u6307\u5BFC\u4EF7" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 123,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u4F30\u4EF7\u8303\u56F4" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 124,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("th", { children: "\u62CD\u5356\u65E5\u671F" }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 125,
              columnNumber: 23
            }, this)
          ] }, void 0, !0, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 116,
            columnNumber: 21
          }, this) }, void 0, !1, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 115,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDEV3("tbody", { children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV3("tr", { children: [
            /* @__PURE__ */ jsxDEV3("td", { children: /* @__PURE__ */ jsxDEV3("a", { href: prop.sourceUrl, target: "_blank", rel: "noopener noreferrer", children: prop.siteName }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 132,
              columnNumber: 27
            }, this) }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 131,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.propertyType }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 136,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.bedrooms }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 137,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.bathrooms }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 138,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.parking }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 139,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.yearBuilt }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 140,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.currentGuidePrice }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 141,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.estimatedValueRange }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 142,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV3("td", { children: prop.auctionDate }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 143,
              columnNumber: 25
            }, this)
          ] }, propIndex, !0, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 130,
            columnNumber: 23
          }, this)) }, void 0, !1, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 128,
            columnNumber: 19
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index-old.tsx",
          lineNumber: 114,
          columnNumber: 17
        }, this) }, void 0, !1, {
          fileName: "app/routes/_index-old.tsx",
          lineNumber: 113,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV3("details", { className: "property-details", children: [
          /* @__PURE__ */ jsxDEV3("summary", { children: "\u67E5\u770B\u8BE6\u7EC6\u4FE1\u606F" }, void 0, !1, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 152,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV3("div", { className: "details-grid", children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV3("div", { className: "detail-card", children: [
            /* @__PURE__ */ jsxDEV3("h4", { children: prop.siteName }, void 0, !1, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 156,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV3("div", { className: "detail-info", children: [
              /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u5730\u5740:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 158,
                  columnNumber: 28
                }, this),
                " ",
                prop.address
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 158,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u571F\u5730\u9762\u79EF:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 159,
                  columnNumber: 28
                }, this),
                " ",
                prop.landSize
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 159,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u5EFA\u7B51\u9762\u79EF:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 160,
                  columnNumber: 28
                }, this),
                " ",
                prop.buildingSize
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 160,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u4F30\u4EF7\u4E2D\u4F4D\u6570:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 161,
                  columnNumber: 28
                }, this),
                " ",
                prop.estimatedValueMid
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 161,
                columnNumber: 25
              }, this),
              prop.features.length > 0 && /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u7279\u8272:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 163,
                  columnNumber: 30
                }, this),
                " ",
                prop.features.join(", ")
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 163,
                columnNumber: 27
              }, this),
              prop.inspectionTimes.length > 0 && /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u5F00\u653E\u68C0\u67E5:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 166,
                  columnNumber: 30
                }, this),
                " ",
                prop.inspectionTimes.join(", ")
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 166,
                columnNumber: 27
              }, this),
              prop.contact !== "N/A" && /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u8054\u7CFB\u65B9\u5F0F:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 169,
                  columnNumber: 30
                }, this),
                " ",
                prop.contact
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 169,
                columnNumber: 27
              }, this),
              prop.description !== "N/A" && /* @__PURE__ */ jsxDEV3("p", { children: [
                /* @__PURE__ */ jsxDEV3("strong", { children: "\u63CF\u8FF0:" }, void 0, !1, {
                  fileName: "app/routes/_index-old.tsx",
                  lineNumber: 172,
                  columnNumber: 30
                }, this),
                " ",
                prop.description
              ] }, void 0, !0, {
                fileName: "app/routes/_index-old.tsx",
                lineNumber: 172,
                columnNumber: 27
              }, this)
            ] }, void 0, !0, {
              fileName: "app/routes/_index-old.tsx",
              lineNumber: 157,
              columnNumber: 23
            }, this)
          ] }, propIndex, !0, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 155,
            columnNumber: 21
          }, this)) }, void 0, !1, {
            fileName: "app/routes/_index-old.tsx",
            lineNumber: 153,
            columnNumber: 17
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index-old.tsx",
          lineNumber: 151,
          columnNumber: 15
        }, this)
      ] }, property.id, !0, {
        fileName: "app/routes/_index-old.tsx",
        lineNumber: 97,
        columnNumber: 13
      }, this))
    ] }, void 0, !0, {
      fileName: "app/routes/_index-old.tsx",
      lineNumber: 93,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDEV3("style", { children: `
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .header {
          text-align: center;
          margin-bottom: 30px;
        }

        .header h1 {
          color: #2c5aa0;
          margin-bottom: 10px;
        }

        .header p {
          color: #666;
          font-size: 16px;
        }

        .search-section {
          text-align: center;
          margin-bottom: 40px;
        }

        .search-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 18px;
          border-radius: 8px;
          cursor: pointer;
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
          min-width: 200px;
        }

        .search-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .search-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .search-button.loading {
          animation: pulse 1s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }

        .error-message {
          background: #fee;
          color: #c33;
          padding: 10px;
          border-radius: 4px;
          margin-top: 10px;
          border: 1px solid #fcc;
        }

        .results-section h2 {
          color: #2c5aa0;
          margin-bottom: 20px;
        }

        .property-card {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin-bottom: 20px;
          overflow: hidden;
        }

        .property-header {
          background: #f8f9fa;
          padding: 20px;
          border-bottom: 1px solid #eee;
        }

        .property-header h3 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
          font-size: 20px;
        }

        .property-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #666;
        }

        .property-meta span {
          margin-right: 15px;
        }

        .delete-button {
          background: #dc3545;
          color: white;
          border: none;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }

        .delete-button:hover {
          background: #c82333;
        }

        .property-table-container {
          overflow-x: auto;
          padding: 0 20px;
        }

        .property-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
          font-size: 14px;
        }

        .property-table th,
        .property-table td {
          padding: 12px 8px;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        .property-table th {
          background: #f8f9fa;
          font-weight: 600;
          color: #2c5aa0;
          white-space: nowrap;
        }

        .property-table td {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }

        .property-table a {
          color: #007bff;
          text-decoration: none;
        }

        .property-table a:hover {
          text-decoration: underline;
        }

        .property-details {
          padding: 20px;
          border-top: 1px solid #eee;
        }

        .property-details summary {
          cursor: pointer;
          font-weight: 600;
          color: #2c5aa0;
          margin-bottom: 15px;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-top: 15px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2c5aa0;
        }

        .detail-card h4 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
        }

        .detail-info p {
          margin: 5px 0;
          font-size: 14px;
          line-height: 1.4;
        }

        .detail-info strong {
          color: #333;
        }

        /* \u79FB\u52A8\u7AEF\u9002\u914D */
        @media (max-width: 768px) {
          .container {
            padding: 10px;
          }

          .property-table-container {
            padding: 0 10px;
          }

          .property-table {
            font-size: 12px;
          }

          .property-table th,
          .property-table td {
            padding: 8px 4px;
          }

          .details-grid {
            grid-template-columns: 1fr;
          }

          .property-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
          }

          .search-button {
            width: 100%;
            max-width: 300px;
          }
        }
      ` }, void 0, !1, {
      fileName: "app/routes/_index-old.tsx",
      lineNumber: 184,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/routes/_index-old.tsx",
    lineNumber: 70,
    columnNumber: 5
  }, this);
}

// app/routes/_index_new.tsx
var index_new_exports = {};
__export(index_new_exports, {
  default: () => Index2
});
import { useState as useState2, useEffect as useEffect2 } from "react";
import { jsxDEV as jsxDEV4 } from "react/jsx-dev-runtime";
function Index2() {
  let [properties, setProperties] = useState2([]), [isLoading, setIsLoading] = useState2(!1), [error, setError] = useState2(null);
  useEffect2(() => {
    let savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []), useEffect2(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);
  let handleSearchClick = async () => {
    setIsLoading(!0), setError(null);
    try {
      let clipboardText = await clipboardUtils.readText();
      if (!clipboardText.trim())
        throw new Error("\u526A\u8D34\u677F\u5185\u5BB9\u4E3A\u7A7A");
      console.log("\u526A\u8D34\u677F\u5185\u5BB9:", clipboardText);
      let response = await fetch("/api/search-property", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ address: clipboardText.trim() })
      });
      if (!response.ok) {
        let errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `API\u8C03\u7528\u5931\u8D25: ${response.status}`);
      }
      let result = await response.json();
      if (result.success && result.data)
        setProperties((prev) => [result.data, ...prev]);
      else
        throw new Error(result.error || "\u641C\u7D22\u5931\u8D25");
    } catch (error2) {
      console.error("\u641C\u7D22\u5931\u8D25:", error2), setError(error2 instanceof Error ? error2.message : String(error2));
    } finally {
      setIsLoading(!1);
    }
  }, handleDeleteProperty = (index) => {
    setProperties((prev) => prev.filter((_, i) => i !== index));
  };
  return /* @__PURE__ */ jsxDEV4("div", { children: [
    /* @__PURE__ */ jsxDEV4("style", { dangerouslySetInnerHTML: { __html: `
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .header {
          text-align: center;
          margin-bottom: 30px;
        }

        .header h1 {
          color: #2c5aa0;
          margin-bottom: 10px;
        }

        .header p {
          color: #666;
          font-size: 16px;
        }

        .search-section {
          text-align: center;
          margin-bottom: 40px;
        }

        .search-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 18px;
          border-radius: 8px;
          cursor: pointer;
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
          min-width: 200px;
        }

        .search-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .search-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .search-button.loading {
          animation: pulse 1s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.7; }
          100% { opacity: 1; }
        }

        .error-message {
          background: #fee;
          color: #c33;
          padding: 10px;
          border-radius: 4px;
          margin-top: 10px;
          border: 1px solid #fcc;
        }

        .results-section h2 {
          color: #2c5aa0;
          margin-bottom: 20px;
        }

        .property-card {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin-bottom: 20px;
          overflow: hidden;
        }

        .property-header {
          background: #f8f9fa;
          padding: 20px;
          border-bottom: 1px solid #eee;
        }

        .property-header h3 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
          font-size: 20px;
        }

        .property-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #666;
          flex-wrap: wrap;
          gap: 10px;
        }

        .property-meta span {
          margin-right: 15px;
        }

        .delete-button {
          background: #dc3545;
          color: white;
          border: none;
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }

        .delete-button:hover {
          background: #c82333;
        }

        .property-table-container {
          overflow-x: auto;
          padding: 20px;
        }

        .property-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 14px;
        }

        .property-table th,
        .property-table td {
          padding: 12px 8px;
          text-align: left;
          border-bottom: 1px solid #eee;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }

        .property-table th {
          background: #f8f9fa;
          font-weight: 600;
          color: #2c5aa0;
        }

        .property-table a {
          color: #007bff;
          text-decoration: none;
        }

        .property-table a:hover {
          text-decoration: underline;
        }

        .property-details {
          padding: 20px;
          border-top: 1px solid #eee;
        }

        .property-details summary {
          cursor: pointer;
          font-weight: 600;
          color: #2c5aa0;
          margin-bottom: 15px;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
          margin-top: 15px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2c5aa0;
        }

        .detail-card h4 {
          margin: 0 0 10px 0;
          color: #2c5aa0;
        }

        .detail-info p {
          margin: 5px 0;
          font-size: 14px;
          line-height: 1.4;
        }

        .detail-info strong {
          color: #333;
        }

        /* \u79FB\u52A8\u7AEF\u9002\u914D */
        @media (max-width: 768px) {
          .container {
            padding: 10px;
          }

          .property-table-container {
            padding: 10px;
          }

          .property-table {
            font-size: 12px;
          }

          .property-table th,
          .property-table td {
            padding: 8px 4px;
          }

          .details-grid {
            grid-template-columns: 1fr;
          }

          .property-meta {
            flex-direction: column;
            align-items: flex-start;
          }

          .search-button {
            width: 100%;
            max-width: 300px;
          }
        }
      ` } }, void 0, !1, {
      fileName: "app/routes/_index_new.tsx",
      lineNumber: 72,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV4("div", { className: "container", children: [
      /* @__PURE__ */ jsxDEV4("div", { className: "header", children: [
        /* @__PURE__ */ jsxDEV4("h1", { children: "\u{1F3E0} \u623F\u4EA7\u4FE1\u606F\u641C\u7D22" }, void 0, !1, {
          fileName: "app/routes/_index_new.tsx",
          lineNumber: 310,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV4("p", { children: "\u70B9\u51FB\u6309\u94AE\u4ECE\u526A\u8D34\u677F\u8BFB\u53D6\u5730\u5740\u5E76\u641C\u7D22\u623F\u4EA7\u4FE1\u606F" }, void 0, !1, {
          fileName: "app/routes/_index_new.tsx",
          lineNumber: 311,
          columnNumber: 11
        }, this)
      ] }, void 0, !0, {
        fileName: "app/routes/_index_new.tsx",
        lineNumber: 309,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV4("div", { className: "search-section", children: [
        /* @__PURE__ */ jsxDEV4(
          "button",
          {
            className: `search-button ${isLoading ? "loading" : ""}`,
            onClick: handleSearchClick,
            disabled: isLoading,
            children: isLoading ? "\u{1F50D} \u641C\u7D22\u4E2D..." : "\u{1F4CB} \u4ECE\u526A\u8D34\u677F\u641C\u7D22"
          },
          void 0,
          !1,
          {
            fileName: "app/routes/_index_new.tsx",
            lineNumber: 315,
            columnNumber: 11
          },
          this
        ),
        error && /* @__PURE__ */ jsxDEV4("div", { className: "error-message", children: [
          "\u274C ",
          error
        ] }, void 0, !0, {
          fileName: "app/routes/_index_new.tsx",
          lineNumber: 324,
          columnNumber: 13
        }, this)
      ] }, void 0, !0, {
        fileName: "app/routes/_index_new.tsx",
        lineNumber: 314,
        columnNumber: 9
      }, this),
      properties.length > 0 && /* @__PURE__ */ jsxDEV4("div", { className: "results-section", children: [
        /* @__PURE__ */ jsxDEV4("h2", { children: [
          "\u641C\u7D22\u7ED3\u679C (",
          properties.length,
          ")"
        ] }, void 0, !0, {
          fileName: "app/routes/_index_new.tsx",
          lineNumber: 332,
          columnNumber: 13
        }, this),
        properties.map((property, index) => /* @__PURE__ */ jsxDEV4("div", { className: "property-card", children: [
          /* @__PURE__ */ jsxDEV4("div", { className: "property-header", children: [
            /* @__PURE__ */ jsxDEV4("h3", { children: property.address }, void 0, !1, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 337,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV4("div", { className: "property-meta", children: [
              /* @__PURE__ */ jsxDEV4("span", { children: [
                "\u{1F5D3}\uFE0F ",
                new Date(property.searchDate).toLocaleString("zh-CN")
              ] }, void 0, !0, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 339,
                columnNumber: 21
              }, this),
              /* @__PURE__ */ jsxDEV4("span", { children: [
                "\u{1F4CA} ",
                property.successfulSources,
                "/",
                property.totalSources,
                " \u6570\u636E\u6E90"
              ] }, void 0, !0, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 340,
                columnNumber: 21
              }, this),
              /* @__PURE__ */ jsxDEV4(
                "button",
                {
                  className: "delete-button",
                  onClick: () => handleDeleteProperty(index),
                  title: "\u5220\u9664\u8FD9\u6761\u8BB0\u5F55",
                  children: "\u{1F5D1}\uFE0F \u5220\u9664"
                },
                void 0,
                !1,
                {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 341,
                  columnNumber: 21
                },
                this
              )
            ] }, void 0, !0, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 338,
              columnNumber: 19
            }, this)
          ] }, void 0, !0, {
            fileName: "app/routes/_index_new.tsx",
            lineNumber: 336,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV4("div", { className: "property-table-container", children: /* @__PURE__ */ jsxDEV4("table", { className: "property-table", children: [
            /* @__PURE__ */ jsxDEV4("thead", { children: /* @__PURE__ */ jsxDEV4("tr", { children: [
              /* @__PURE__ */ jsxDEV4("th", { children: "\u7F51\u7AD9" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 355,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u623F\u578B" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 356,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u5367\u5BA4" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 357,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u6D74\u5BA4" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 358,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u505C\u8F66" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 359,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u5EFA\u6210\u5E74\u4EFD" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 360,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u6307\u5BFC\u4EF7" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 361,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u4F30\u4EF7\u8303\u56F4" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 362,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("th", { children: "\u62CD\u5356\u65E5\u671F" }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 363,
                columnNumber: 25
              }, this)
            ] }, void 0, !0, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 354,
              columnNumber: 23
            }, this) }, void 0, !1, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 353,
              columnNumber: 21
            }, this),
            /* @__PURE__ */ jsxDEV4("tbody", { children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV4("tr", { children: [
              /* @__PURE__ */ jsxDEV4("td", { children: /* @__PURE__ */ jsxDEV4("a", { href: prop.sourceUrl, target: "_blank", rel: "noopener noreferrer", children: prop.siteName }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 370,
                columnNumber: 29
              }, this) }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 369,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.propertyType }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 374,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.bedrooms }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 375,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.bathrooms }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 376,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.parking }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 377,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.yearBuilt }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 378,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.currentGuidePrice }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 379,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.estimatedValueRange }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 380,
                columnNumber: 27
              }, this),
              /* @__PURE__ */ jsxDEV4("td", { children: prop.auctionDate }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 381,
                columnNumber: 27
              }, this)
            ] }, propIndex, !0, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 368,
              columnNumber: 25
            }, this)) }, void 0, !1, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 366,
              columnNumber: 21
            }, this)
          ] }, void 0, !0, {
            fileName: "app/routes/_index_new.tsx",
            lineNumber: 352,
            columnNumber: 19
          }, this) }, void 0, !1, {
            fileName: "app/routes/_index_new.tsx",
            lineNumber: 351,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV4("details", { className: "property-details", children: [
            /* @__PURE__ */ jsxDEV4("summary", { children: "\u67E5\u770B\u8BE6\u7EC6\u4FE1\u606F" }, void 0, !1, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 390,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV4("div", { className: "details-grid", children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV4("div", { className: "detail-card", children: [
              /* @__PURE__ */ jsxDEV4("h4", { children: prop.siteName }, void 0, !1, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 394,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV4("div", { className: "detail-info", children: [
                /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u5730\u5740:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 396,
                    columnNumber: 30
                  }, this),
                  " ",
                  prop.address
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 396,
                  columnNumber: 27
                }, this),
                /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u571F\u5730\u9762\u79EF:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 397,
                    columnNumber: 30
                  }, this),
                  " ",
                  prop.landSize
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 397,
                  columnNumber: 27
                }, this),
                /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u5EFA\u7B51\u9762\u79EF:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 398,
                    columnNumber: 30
                  }, this),
                  " ",
                  prop.buildingSize
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 398,
                  columnNumber: 27
                }, this),
                /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u4F30\u4EF7\u4E2D\u4F4D\u6570:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 399,
                    columnNumber: 30
                  }, this),
                  " ",
                  prop.estimatedValueMid
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 399,
                  columnNumber: 27
                }, this),
                prop.features.length > 0 && /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u7279\u8272:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 401,
                    columnNumber: 32
                  }, this),
                  " ",
                  prop.features.join(", ")
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 401,
                  columnNumber: 29
                }, this),
                prop.inspectionTimes.length > 0 && /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u5F00\u653E\u68C0\u67E5:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 404,
                    columnNumber: 32
                  }, this),
                  " ",
                  prop.inspectionTimes.join(", ")
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 404,
                  columnNumber: 29
                }, this),
                prop.contact !== "N/A" && /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u8054\u7CFB\u65B9\u5F0F:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 407,
                    columnNumber: 32
                  }, this),
                  " ",
                  prop.contact
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 407,
                  columnNumber: 29
                }, this),
                prop.description !== "N/A" && /* @__PURE__ */ jsxDEV4("p", { children: [
                  /* @__PURE__ */ jsxDEV4("strong", { children: "\u63CF\u8FF0:" }, void 0, !1, {
                    fileName: "app/routes/_index_new.tsx",
                    lineNumber: 410,
                    columnNumber: 32
                  }, this),
                  " ",
                  prop.description
                ] }, void 0, !0, {
                  fileName: "app/routes/_index_new.tsx",
                  lineNumber: 410,
                  columnNumber: 29
                }, this)
              ] }, void 0, !0, {
                fileName: "app/routes/_index_new.tsx",
                lineNumber: 395,
                columnNumber: 25
              }, this)
            ] }, propIndex, !0, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 393,
              columnNumber: 23
            }, this)) }, void 0, !1, {
              fileName: "app/routes/_index_new.tsx",
              lineNumber: 391,
              columnNumber: 19
            }, this)
          ] }, void 0, !0, {
            fileName: "app/routes/_index_new.tsx",
            lineNumber: 389,
            columnNumber: 17
          }, this)
        ] }, property.id, !0, {
          fileName: "app/routes/_index_new.tsx",
          lineNumber: 335,
          columnNumber: 15
        }, this))
      ] }, void 0, !0, {
        fileName: "app/routes/_index_new.tsx",
        lineNumber: 331,
        columnNumber: 11
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index_new.tsx",
      lineNumber: 308,
      columnNumber: 7
    }, this)
  ] }, void 0, !0, {
    fileName: "app/routes/_index_new.tsx",
    lineNumber: 71,
    columnNumber: 5
  }, this);
}

// app/routes/_index.tsx
var index_exports = {};
__export(index_exports, {
  default: () => Index3
});
import { useState as useState3, useEffect as useEffect3 } from "react";
import { jsxDEV as jsxDEV5 } from "react/jsx-dev-runtime";
function Index3() {
  let [properties, setProperties] = useState3([]), [isLoading, setIsLoading] = useState3(!1), [error, setError] = useState3(null);
  useEffect3(() => {
    let savedProperties = localStorageUtils.loadProperties();
    setProperties(savedProperties);
  }, []), useEffect3(() => {
    localStorageUtils.saveProperties(properties);
  }, [properties]);
  let handleSearchClick = async () => {
    setIsLoading(!0), setError(null);
    try {
      let clipboardText = await clipboardUtils.readText();
      if (!clipboardText.trim())
        throw new Error("\u526A\u8D34\u677F\u5185\u5BB9\u4E3A\u7A7A");
      console.log("\u526A\u8D34\u677F\u5185\u5BB9:", clipboardText);
      let response = await fetch("/api/search-property", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ address: clipboardText.trim() })
      });
      if (!response.ok) {
        let errorData = await response.json();
        throw new Error(errorData.error || `API\u8C03\u7528\u5931\u8D25: ${response.status}`);
      }
      let result = await response.json();
      if (result.success && result.data)
        setProperties((prev) => [result.data, ...prev]);
      else
        throw new Error(result.error || "\u641C\u7D22\u5931\u8D25");
    } catch (error2) {
      console.error("\u641C\u7D22\u5931\u8D25:", error2), setError(error2 instanceof Error ? error2.message : String(error2));
    } finally {
      setIsLoading(!1);
    }
  }, handleDeleteProperty = (index) => {
    setProperties((prev) => prev.filter((_, i) => i !== index));
  }, buttonStyle = {
    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    color: "white",
    border: "none",
    padding: "15px 30px",
    fontSize: "18px",
    borderRadius: "8px",
    cursor: isLoading ? "not-allowed" : "pointer",
    boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
    minWidth: "200px",
    opacity: isLoading ? 0.7 : 1
  }, containerStyle = {
    maxWidth: "1200px",
    margin: "0 auto",
    padding: "20px",
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  }, errorStyle = {
    background: "#fee",
    color: "#c33",
    padding: "10px",
    borderRadius: "4px",
    marginTop: "10px",
    border: "1px solid #fcc"
  }, cardStyle = {
    background: "white",
    borderRadius: "8px",
    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
    marginBottom: "20px",
    overflow: "hidden"
  }, headerStyle = {
    background: "#f8f9fa",
    padding: "20px",
    borderBottom: "1px solid #eee"
  }, tableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    margin: "20px 0",
    fontSize: "14px"
  }, thStyle = {
    padding: "12px 8px",
    textAlign: "left",
    borderBottom: "1px solid #eee",
    background: "#f8f9fa",
    fontWeight: 600,
    color: "#2c5aa0"
  }, tdStyle = {
    padding: "12px 8px",
    textAlign: "left",
    borderBottom: "1px solid #eee",
    maxWidth: "120px",
    overflow: "hidden",
    textOverflow: "ellipsis"
  };
  return /* @__PURE__ */ jsxDEV5("div", { style: containerStyle, children: [
    /* @__PURE__ */ jsxDEV5("div", { style: { textAlign: "center", marginBottom: "30px" }, children: [
      /* @__PURE__ */ jsxDEV5("h1", { style: { color: "#2c5aa0", marginBottom: "10px" }, children: "\u{1F3E0} \u623F\u4EA7\u4FE1\u606F\u641C\u7D22" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 140,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV5("p", { style: { color: "#666", fontSize: "16px" }, children: "\u70B9\u51FB\u6309\u94AE\u4ECE\u526A\u8D34\u677F\u8BFB\u53D6\u5730\u5740\u5E76\u641C\u7D22\u623F\u4EA7\u4FE1\u606F" }, void 0, !1, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 141,
        columnNumber: 9
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 139,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV5("div", { style: { textAlign: "center", marginBottom: "40px" }, children: [
      /* @__PURE__ */ jsxDEV5(
        "button",
        {
          style: buttonStyle,
          onClick: handleSearchClick,
          disabled: isLoading,
          children: isLoading ? "\u{1F50D} \u641C\u7D22\u4E2D..." : "\u{1F4CB} \u4ECE\u526A\u8D34\u677F\u641C\u7D22"
        },
        void 0,
        !1,
        {
          fileName: "app/routes/_index.tsx",
          lineNumber: 145,
          columnNumber: 9
        },
        this
      ),
      error && /* @__PURE__ */ jsxDEV5("div", { style: errorStyle, children: [
        "\u274C ",
        error
      ] }, void 0, !0, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 154,
        columnNumber: 11
      }, this)
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 144,
      columnNumber: 7
    }, this),
    properties.length > 0 && /* @__PURE__ */ jsxDEV5("div", { children: [
      /* @__PURE__ */ jsxDEV5("h2", { style: { color: "#2c5aa0", marginBottom: "20px" }, children: [
        "\u641C\u7D22\u7ED3\u679C (",
        properties.length,
        ")"
      ] }, void 0, !0, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 162,
        columnNumber: 11
      }, this),
      properties.map((property, index) => /* @__PURE__ */ jsxDEV5("div", { style: cardStyle, children: [
        /* @__PURE__ */ jsxDEV5("div", { style: headerStyle, children: [
          /* @__PURE__ */ jsxDEV5("h3", { style: { margin: "0 0 10px 0", color: "#2c5aa0", fontSize: "20px" }, children: property.address }, void 0, !1, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 167,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV5("div", { style: { display: "flex", justifyContent: "space-between", alignItems: "center", fontSize: "14px", color: "#666" }, children: [
            /* @__PURE__ */ jsxDEV5("div", { children: [
              /* @__PURE__ */ jsxDEV5("span", { style: { marginRight: "15px" }, children: [
                "\u{1F5D3}\uFE0F ",
                new Date(property.searchDate).toLocaleString("zh-CN")
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 170,
                columnNumber: 21
              }, this),
              /* @__PURE__ */ jsxDEV5("span", { children: [
                "\u{1F4CA} ",
                property.successfulSources,
                "/",
                property.totalSources,
                " \u6570\u636E\u6E90"
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 171,
                columnNumber: 21
              }, this)
            ] }, void 0, !0, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 169,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV5(
              "button",
              {
                style: {
                  background: "#dc3545",
                  color: "white",
                  border: "none",
                  padding: "5px 10px",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "12px"
                },
                onClick: () => handleDeleteProperty(index),
                title: "\u5220\u9664\u8FD9\u6761\u8BB0\u5F55",
                children: "\u{1F5D1}\uFE0F"
              },
              void 0,
              !1,
              {
                fileName: "app/routes/_index.tsx",
                lineNumber: 173,
                columnNumber: 19
              },
              this
            )
          ] }, void 0, !0, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 168,
            columnNumber: 17
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 166,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("div", { style: { overflowX: "auto", padding: "0 20px" }, children: /* @__PURE__ */ jsxDEV5("table", { style: tableStyle, children: [
          /* @__PURE__ */ jsxDEV5("thead", { children: /* @__PURE__ */ jsxDEV5("tr", { children: [
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u7F51\u7AD9" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 195,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u623F\u578B" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 196,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u5367\u5BA4" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 197,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u6D74\u5BA4" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 198,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u505C\u8F66" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 199,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u5EFA\u6210\u5E74\u4EFD" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 200,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u6307\u5BFC\u4EF7" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 201,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u4F30\u4EF7\u8303\u56F4" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 202,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("th", { style: thStyle, children: "\u62CD\u5356\u65E5\u671F" }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 203,
              columnNumber: 23
            }, this)
          ] }, void 0, !0, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 194,
            columnNumber: 21
          }, this) }, void 0, !1, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 193,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDEV5("tbody", { children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV5("tr", { children: [
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: /* @__PURE__ */ jsxDEV5("a", { href: prop.sourceUrl, target: "_blank", rel: "noopener noreferrer", style: { color: "#007bff", textDecoration: "none" }, children: prop.siteName }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 210,
              columnNumber: 27
            }, this) }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 209,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.propertyType }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 214,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.bedrooms }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 215,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.bathrooms }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 216,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.parking }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 217,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.yearBuilt }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 218,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.currentGuidePrice }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 219,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.estimatedValueRange }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 220,
              columnNumber: 25
            }, this),
            /* @__PURE__ */ jsxDEV5("td", { style: tdStyle, children: prop.auctionDate }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 221,
              columnNumber: 25
            }, this)
          ] }, propIndex, !0, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 208,
            columnNumber: 23
          }, this)) }, void 0, !1, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 206,
            columnNumber: 19
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 192,
          columnNumber: 17
        }, this) }, void 0, !1, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 191,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV5("details", { style: { padding: "20px", borderTop: "1px solid #eee" }, children: [
          /* @__PURE__ */ jsxDEV5("summary", { style: { cursor: "pointer", fontWeight: 600, color: "#2c5aa0", marginBottom: "15px" }, children: "\u67E5\u770B\u8BE6\u7EC6\u4FE1\u606F" }, void 0, !1, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 229,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV5("div", { style: { display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: "20px", marginTop: "15px" }, children: property.properties.map((prop, propIndex) => /* @__PURE__ */ jsxDEV5("div", { style: {
            background: "#f8f9fa",
            padding: "15px",
            borderRadius: "6px",
            borderLeft: "4px solid #2c5aa0"
          }, children: [
            /* @__PURE__ */ jsxDEV5("h4", { style: { margin: "0 0 10px 0", color: "#2c5aa0" }, children: prop.siteName }, void 0, !1, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 240,
              columnNumber: 23
            }, this),
            /* @__PURE__ */ jsxDEV5("div", { children: [
              /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u5730\u5740:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 243,
                  columnNumber: 27
                }, this),
                " ",
                prop.address
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 242,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u571F\u5730\u9762\u79EF:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 246,
                  columnNumber: 27
                }, this),
                " ",
                prop.landSize
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 245,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u5EFA\u7B51\u9762\u79EF:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 249,
                  columnNumber: 27
                }, this),
                " ",
                prop.buildingSize
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 248,
                columnNumber: 25
              }, this),
              /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u4F30\u4EF7\u4E2D\u4F4D\u6570:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 252,
                  columnNumber: 27
                }, this),
                " ",
                prop.estimatedValueMid
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 251,
                columnNumber: 25
              }, this),
              prop.features.length > 0 && /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u7279\u8272:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 256,
                  columnNumber: 29
                }, this),
                " ",
                prop.features.join(", ")
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 255,
                columnNumber: 27
              }, this),
              prop.inspectionTimes.length > 0 && /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u5F00\u653E\u68C0\u67E5:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 261,
                  columnNumber: 29
                }, this),
                " ",
                prop.inspectionTimes.join(", ")
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 260,
                columnNumber: 27
              }, this),
              prop.contact !== "N/A" && /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u8054\u7CFB\u65B9\u5F0F:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 266,
                  columnNumber: 29
                }, this),
                " ",
                prop.contact
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 265,
                columnNumber: 27
              }, this),
              prop.description !== "N/A" && /* @__PURE__ */ jsxDEV5("p", { style: { margin: "5px 0", fontSize: "14px", lineHeight: "1.4" }, children: [
                /* @__PURE__ */ jsxDEV5("strong", { style: { color: "#333" }, children: "\u63CF\u8FF0:" }, void 0, !1, {
                  fileName: "app/routes/_index.tsx",
                  lineNumber: 271,
                  columnNumber: 29
                }, this),
                " ",
                prop.description
              ] }, void 0, !0, {
                fileName: "app/routes/_index.tsx",
                lineNumber: 270,
                columnNumber: 27
              }, this)
            ] }, void 0, !0, {
              fileName: "app/routes/_index.tsx",
              lineNumber: 241,
              columnNumber: 23
            }, this)
          ] }, propIndex, !0, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 234,
            columnNumber: 21
          }, this)) }, void 0, !1, {
            fileName: "app/routes/_index.tsx",
            lineNumber: 232,
            columnNumber: 17
          }, this)
        ] }, void 0, !0, {
          fileName: "app/routes/_index.tsx",
          lineNumber: 228,
          columnNumber: 15
        }, this)
      ] }, property.id, !0, {
        fileName: "app/routes/_index.tsx",
        lineNumber: 165,
        columnNumber: 13
      }, this))
    ] }, void 0, !0, {
      fileName: "app/routes/_index.tsx",
      lineNumber: 161,
      columnNumber: 9
    }, this)
  ] }, void 0, !0, {
    fileName: "app/routes/_index.tsx",
    lineNumber: 138,
    columnNumber: 5
  }, this);
}

// server-assets-manifest:@remix-run/dev/assets-manifest
var assets_manifest_default = { entry: { module: "/build/entry.client-BPYWITAV.js", imports: ["/build/_shared/chunk-O4BRYNJ4.js", "/build/_shared/chunk-V3BJQ67B.js", "/build/_shared/chunk-U4FRFQSK.js", "/build/_shared/chunk-X3T7OMQU.js", "/build/_shared/chunk-XGOTYLZ5.js", "/build/_shared/chunk-7M6SC7J5.js", "/build/_shared/chunk-UWV35TSL.js", "/build/_shared/chunk-PNG5AS42.js"] }, routes: { root: { id: "root", parentId: void 0, path: "", index: void 0, caseSensitive: void 0, module: "/build/root-BKU7K54G.js", imports: void 0, hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/_index": { id: "routes/_index", parentId: "root", path: void 0, index: !0, caseSensitive: void 0, module: "/build/routes/_index-GZX4PWJK.js", imports: ["/build/_shared/chunk-MNIL4YD6.js"], hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/_index-old": { id: "routes/_index-old", parentId: "root", path: void 0, index: void 0, caseSensitive: void 0, module: "/build/routes/_index-old-NSHHUIEC.js", imports: ["/build/_shared/chunk-MNIL4YD6.js"], hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/_index_new": { id: "routes/_index_new", parentId: "root", path: void 0, index: void 0, caseSensitive: void 0, module: "/build/routes/_index_new-WFDJIEW5.js", imports: ["/build/_shared/chunk-MNIL4YD6.js"], hasAction: !1, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 }, "routes/api.search-property": { id: "routes/api.search-property", parentId: "root", path: "api/search-property", index: void 0, caseSensitive: void 0, module: "/build/routes/api.search-property-JHHPWYLI.js", imports: void 0, hasAction: !0, hasLoader: !1, hasClientAction: !1, hasClientLoader: !1, hasErrorBoundary: !1 } }, version: "81fde8d0", hmr: { runtime: "/build/_shared\\chunk-X3T7OMQU.js", timestamp: 1758896614663 }, url: "/build/manifest-81FDE8D0.js" };

// server-entry-module:@remix-run/dev/server-build
var mode = "development", assetsBuildDirectory = "public\\build", future = { v3_fetcherPersist: !1, v3_relativeSplatPath: !1, v3_throwAbortReason: !1, v3_routeConfig: !1, v3_singleFetch: !1, v3_lazyRouteDiscovery: !1, unstable_optimizeDeps: !1 }, publicPath = "/build/", entry = { module: entry_server_exports }, routes = {
  root: {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: root_exports
  },
  "routes/api.search-property": {
    id: "routes/api.search-property",
    parentId: "root",
    path: "api/search-property",
    index: void 0,
    caseSensitive: void 0,
    module: api_search_property_exports
  },
  "routes/_index-old": {
    id: "routes/_index-old",
    parentId: "root",
    path: void 0,
    index: void 0,
    caseSensitive: void 0,
    module: index_old_exports
  },
  "routes/_index_new": {
    id: "routes/_index_new",
    parentId: "root",
    path: void 0,
    index: void 0,
    caseSensitive: void 0,
    module: index_new_exports
  },
  "routes/_index": {
    id: "routes/_index",
    parentId: "root",
    path: void 0,
    index: !0,
    caseSensitive: void 0,
    module: index_exports
  }
};
export {
  assets_manifest_default as assets,
  assetsBuildDirectory,
  entry,
  future,
  mode,
  publicPath,
  routes
};
//# sourceMappingURL=index.js.map
