{"version": 3, "sources": ["../app/entry.server.tsx", "../app/root.tsx", "../app/routes/api.search-property.ts", "../app/utils/server-search-simple.ts", "../app/routes/_index-old.tsx", "../app/utils/property-search.ts", "../app/routes/_index_new.tsx", "../app/routes/_index.tsx", "server-assets-manifest:@remix-run/dev/assets-manifest", "server-entry-module:@remix-run/dev/server-build"], "sourcesContent": ["import type { EntryContext } from \"@remix-run/node\";\r\nimport { RemixServer } from \"@remix-run/react\";\r\nimport { renderToString } from \"react-dom/server\";\r\n\r\nexport default function handleRequest(\r\n  request: Request,\r\n  responseStatusCode: number,\r\n  responseHeaders: Headers,\r\n  remixContext: EntryContext\r\n) {\r\n  const markup = renderToString(\r\n    <RemixServer context={remixContext} url={request.url} />\r\n  );\r\n\r\n  responseHeaders.set(\"Content-Type\", \"text/html\");\r\n\r\n  return new Response(\"<!DOCTYPE html>\" + markup, {\r\n    status: responseStatusCode,\r\n    headers: responseHeaders,\r\n  });\r\n}", "import {\r\n  Links,\r\n  LiveReload,\r\n  Meta,\r\n  Outlet,\r\n  Scripts,\r\n  ScrollRestoration,\r\n} from \"@remix-run/react\";\r\n\r\nexport default function App() {\r\n  return (\r\n    <html lang=\"zh-CN\">\r\n      <head>\r\n        <meta charSet=\"utf-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <Meta />\r\n        <Links />\r\n        <style>{`\r\n          * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n          }\r\n          \r\n          body {\r\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif;\r\n            background-color: #f5f5f5;\r\n            color: #333;\r\n          }\r\n          \r\n          .container {\r\n            max-width: 1200px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n          }\r\n          \r\n          @media (max-width: 768px) {\r\n            .container {\r\n              padding: 10px;\r\n            }\r\n          }\r\n        `}</style>\r\n      </head>\r\n      <body>\r\n        <Outlet />\r\n        <ScrollRestoration />\r\n        <Scripts />\r\n        <LiveReload />\r\n      </body>\r\n    </html>\r\n  );\r\n}", "import { json } from \"@remix-run/node\";\r\nimport type { ActionFunctionArgs } from \"@remix-run/node\";\r\nimport { executeSearchWorkflow } from \"~/utils/server-search-simple\";\r\n\r\nexport async function action({ request }: ActionFunctionArgs) {\r\n  if (request.method !== \"POST\") {\r\n    return json({ error: \"Method not allowed\" }, { status: 405 });\r\n  }\r\n\r\n  try {\r\n    const { address } = await request.json();\r\n    \r\n    if (!address || typeof address !== 'string') {\r\n      return json({ error: \"Invalid address provided\" }, { status: 400 });\r\n    }\r\n\r\n    console.log(`🔍 API: 开始处理房产地址: ${address}`);\r\n    \r\n    // 执行完整的搜索工作流\r\n    const result = await executeSearchWorkflow(address);\r\n    \r\n    if (result.success) {\r\n      return json(result);\r\n    } else {\r\n      return json(result, { status: 500 });\r\n    }\r\n    \r\n  } catch (error) {\r\n    console.error('❌ API: 工作流程失败:', error);\r\n    return json({\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    }, { status: 500 });\r\n  }\r\n}", "// 简化的服务端搜索 - 直接集成所有功能\r\nimport Exa from \"exa-js\";\r\n\r\n// 检查必需的环境变量\r\nfunction checkEnvironmentVariables() {\r\n  const requiredVars = ['EXA_API_KEY'];\r\n  const missing = requiredVars.filter(varName => !process.env[varName]);\r\n  \r\n  if (missing.length > 0) {\r\n    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}。请检查 .env 文件是否正确配置。`);\r\n  }\r\n}\r\n\r\n// EXA搜索实现\r\nasync function searchPropertyInfo(address: string): Promise<any[]> {\r\n  try {\r\n    console.log(`🔍 EXA搜索: ${address}`);\r\n    \r\n    const exa = new Exa(process.env.EXA_API_KEY);\r\n    \r\n    const searchQuery = `${address} property details real estate listing Australia`;\r\n    console.log(`📝 搜索查询: ${searchQuery}`);\r\n    \r\n    const result = await exa.search(searchQuery, {\r\n      type: \"neural\",\r\n      useAutoprompt: true,\r\n      numResults: 5,\r\n      includeDomains: [\r\n        'domain.com.au',\r\n        'realestate.com.au', \r\n        'realtor.com',\r\n        'view.com.au',\r\n        'onthehouse.com.au',\r\n        'propertyvalue.com.au'\r\n      ]\r\n    });\r\n    \r\n    console.log(`✅ EXA搜索完成，找到 ${result.results.length} 个结果`);\r\n    return result.results;\r\n  } catch (error) {\r\n    console.error('❌ EXA搜索失败:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// 简化的房产信息处理\r\nfunction processPropertyData(searchResults: any[], searchAddress: string): any[] {\r\n  return searchResults.map((result, index) => {\r\n    // 从文本内容中提取基本信息\r\n    const text = result.text || '';\r\n    const title = result.title || '';\r\n    \r\n    // 简单的信息提取（正则表达式匹配）\r\n    const bedroomsMatch = text.match(/(\\d+)\\s*(bed|bedroom|br)/i);\r\n    const bathroomsMatch = text.match(/(\\d+)\\s*(bath|bathroom|ba)/i);\r\n    const priceMatch = text.match(/\\$[\\d,]+/g);\r\n    \r\n    return {\r\n      id: index + 1,\r\n      title: title,\r\n      url: result.url,\r\n      siteName: new URL(result.url).hostname,\r\n      address: title.includes(searchAddress) ? searchAddress : 'Address from search',\r\n      bedrooms: bedroomsMatch ? bedroomsMatch[1] : 'N/A',\r\n      bathrooms: bathroomsMatch ? bathroomsMatch[1] : 'N/A',\r\n      priceInfo: priceMatch ? priceMatch[0] : 'N/A',\r\n      description: text.substring(0, 300) + '...',\r\n      sourceUrl: result.url,\r\n      extractedAt: new Date().toISOString()\r\n    };\r\n  });\r\n}\r\n\r\n// 执行简化的搜索工作流\r\nexport async function executeSearchWorkflow(address: string) {\r\n  try {\r\n    console.log(`🔍 服务端: 开始处理房产地址: ${address}`);\r\n    \r\n    // 检查环境变量\r\n    checkEnvironmentVariables();\r\n    \r\n    // 第1步：EXA搜索\r\n    console.log('📡 服务端: 第1步：EXA搜索...');\r\n    const searchResults = await searchPropertyInfo(address);\r\n    \r\n    if (!searchResults || searchResults.length === 0) {\r\n      throw new Error('EXA搜索未找到相关结果');\r\n    }\r\n    \r\n    // 第2步：处理搜索结果\r\n    console.log('📊 服务端: 第2步：处理搜索结果...');\r\n    const processedData = processPropertyData(searchResults, address);\r\n    \r\n    // 构建最终数据结构\r\n    const summaryData = {\r\n      id: `property-${Date.now()}`,\r\n      address: address,\r\n      searchDate: new Date().toISOString(),\r\n      totalResults: searchResults.length,\r\n      properties: processedData,\r\n      metadata: {\r\n        searchQuery: `${address} property details real estate listing Australia`,\r\n        domains: ['domain.com.au', 'realestate.com.au', 'realtor.com', 'view.com.au'],\r\n        processedAt: new Date().toISOString()\r\n      }\r\n    };\r\n    \r\n    console.log('✅ 服务端: 工作流程完成');\r\n    return {\r\n      success: true,\r\n      data: summaryData\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('❌ 服务端: 工作流程失败:', error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    };\r\n  }\r\n}", "import { useState, useEffect } from \"react\";\r\nimport { localStorageUtils, clipboardUtils, type PropertyData } from \"~/utils/property-search\";\r\n\r\nexport default function Index() {\r\n  const [properties, setProperties] = useState<PropertyData[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 从localStorage加载数据\r\n  useEffect(() => {\r\n    const savedProperties = localStorageUtils.loadProperties();\r\n    setProperties(savedProperties);\r\n  }, []);\r\n\r\n  // 保存到localStorage\r\n  useEffect(() => {\r\n    localStorageUtils.saveProperties(properties);\r\n  }, [properties]);\r\n\r\n  const handleSearchClick = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // 读取剪贴板内容\r\n      const clipboardText = await clipboardUtils.readText();\r\n      \r\n      if (!clipboardText.trim()) {\r\n        throw new Error('剪贴板内容为空');\r\n      }\r\n\r\n      console.log('剪贴板内容:', clipboardText);\r\n\r\n      // 调用API进行搜索和抓取\r\n      const response = await fetch('/api/search-property', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ address: clipboardText.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || `API调用失败: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      \r\n      if (result.success && result.data) {\r\n        // 添加新的房产数据到列表开头\r\n        setProperties(prev => [result.data, ...prev]);\r\n      } else {\r\n        throw new Error(result.error || '搜索失败');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('搜索失败:', error);\r\n      setError(error instanceof Error ? error.message : String(error));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteProperty = (index: number) => {\r\n    setProperties(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  return (\r\n    <div className=\"container\">\r\n      <div className=\"header\">\r\n        <h1>🏠 房产信息搜索</h1>\r\n        <p>点击按钮从剪贴板读取地址并搜索房产信息</p>\r\n      </div>\r\n\r\n      <div className=\"search-section\">\r\n        <button \r\n          className={`search-button ${isLoading ? 'loading' : ''}`}\r\n          onClick={handleSearchClick}\r\n          disabled={isLoading}\r\n        >\r\n          {isLoading ? '🔍 搜索中...' : '📋 从剪贴板搜索'}\r\n        </button>\r\n        \r\n        {error && (\r\n          <div className=\"error-message\">\r\n            ❌ {error}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {properties.length > 0 && (\r\n        <div className=\"results-section\">\r\n          <h2>搜索结果 ({properties.length})</h2>\r\n          \r\n          {properties.map((property, index) => (\r\n            <div key={property.id} className=\"property-card\">\r\n              <div className=\"property-header\">\r\n                <h3>{property.address}</h3>\r\n                <div className=\"property-meta\">\r\n                  <span>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>\r\n                  <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>\r\n                  <button \r\n                    className=\"delete-button\"\r\n                    onClick={() => handleDeleteProperty(index)}\r\n                    title=\"删除这条记录\"\r\n                  >\r\n                    🗑️\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"property-table-container\">\r\n                <table className=\"property-table\">\r\n                  <thead>\r\n                    <tr>\r\n                      <th>网站</th>\r\n                      <th>房型</th>\r\n                      <th>卧室</th>\r\n                      <th>浴室</th>\r\n                      <th>停车</th>\r\n                      <th>建成年份</th>\r\n                      <th>指导价</th>\r\n                      <th>估价范围</th>\r\n                      <th>拍卖日期</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {property.properties.map((prop, propIndex) => (\r\n                      <tr key={propIndex}>\r\n                        <td>\r\n                          <a href={prop.sourceUrl} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                            {prop.siteName}\r\n                          </a>\r\n                        </td>\r\n                        <td>{prop.propertyType}</td>\r\n                        <td>{prop.bedrooms}</td>\r\n                        <td>{prop.bathrooms}</td>\r\n                        <td>{prop.parking}</td>\r\n                        <td>{prop.yearBuilt}</td>\r\n                        <td>{prop.currentGuidePrice}</td>\r\n                        <td>{prop.estimatedValueRange}</td>\r\n                        <td>{prop.auctionDate}</td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              {/* 详细信息折叠区域 */}\r\n              <details className=\"property-details\">\r\n                <summary>查看详细信息</summary>\r\n                <div className=\"details-grid\">\r\n                  {property.properties.map((prop, propIndex) => (\r\n                    <div key={propIndex} className=\"detail-card\">\r\n                      <h4>{prop.siteName}</h4>\r\n                      <div className=\"detail-info\">\r\n                        <p><strong>地址:</strong> {prop.address}</p>\r\n                        <p><strong>土地面积:</strong> {prop.landSize}</p>\r\n                        <p><strong>建筑面积:</strong> {prop.buildingSize}</p>\r\n                        <p><strong>估价中位数:</strong> {prop.estimatedValueMid}</p>\r\n                        {prop.features.length > 0 && (\r\n                          <p><strong>特色:</strong> {prop.features.join(', ')}</p>\r\n                        )}\r\n                        {prop.inspectionTimes.length > 0 && (\r\n                          <p><strong>开放检查:</strong> {prop.inspectionTimes.join(', ')}</p>\r\n                        )}\r\n                        {prop.contact !== 'N/A' && (\r\n                          <p><strong>联系方式:</strong> {prop.contact}</p>\r\n                        )}\r\n                        {prop.description !== 'N/A' && (\r\n                          <p><strong>描述:</strong> {prop.description}</p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </details>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      <style>{`\r\n        .container {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n          padding: 20px;\r\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n        }\r\n\r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n\r\n        .header h1 {\r\n          color: #2c5aa0;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .header p {\r\n          color: #666;\r\n          font-size: 16px;\r\n        }\r\n\r\n        .search-section {\r\n          text-align: center;\r\n          margin-bottom: 40px;\r\n        }\r\n\r\n        .search-button {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: white;\r\n          border: none;\r\n          padding: 15px 30px;\r\n          font-size: 18px;\r\n          border-radius: 8px;\r\n          cursor: pointer;\r\n          box-shadow: 0 4px 15px rgba(0,0,0,0.2);\r\n          transition: all 0.3s ease;\r\n          min-width: 200px;\r\n        }\r\n\r\n        .search-button:hover:not(:disabled) {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 6px 20px rgba(0,0,0,0.3);\r\n        }\r\n\r\n        .search-button:disabled {\r\n          opacity: 0.7;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .search-button.loading {\r\n          animation: pulse 1s infinite;\r\n        }\r\n\r\n        @keyframes pulse {\r\n          0% { opacity: 1; }\r\n          50% { opacity: 0.7; }\r\n          100% { opacity: 1; }\r\n        }\r\n\r\n        .error-message {\r\n          background: #fee;\r\n          color: #c33;\r\n          padding: 10px;\r\n          border-radius: 4px;\r\n          margin-top: 10px;\r\n          border: 1px solid #fcc;\r\n        }\r\n\r\n        .results-section h2 {\r\n          color: #2c5aa0;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .property-card {\r\n          background: white;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\r\n          margin-bottom: 20px;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .property-header {\r\n          background: #f8f9fa;\r\n          padding: 20px;\r\n          border-bottom: 1px solid #eee;\r\n        }\r\n\r\n        .property-header h3 {\r\n          margin: 0 0 10px 0;\r\n          color: #2c5aa0;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .property-meta {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          font-size: 14px;\r\n          color: #666;\r\n        }\r\n\r\n        .property-meta span {\r\n          margin-right: 15px;\r\n        }\r\n\r\n        .delete-button {\r\n          background: #dc3545;\r\n          color: white;\r\n          border: none;\r\n          padding: 5px 10px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .delete-button:hover {\r\n          background: #c82333;\r\n        }\r\n\r\n        .property-table-container {\r\n          overflow-x: auto;\r\n          padding: 0 20px;\r\n        }\r\n\r\n        .property-table {\r\n          width: 100%;\r\n          border-collapse: collapse;\r\n          margin: 20px 0;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .property-table th,\r\n        .property-table td {\r\n          padding: 12px 8px;\r\n          text-align: left;\r\n          border-bottom: 1px solid #eee;\r\n        }\r\n\r\n        .property-table th {\r\n          background: #f8f9fa;\r\n          font-weight: 600;\r\n          color: #2c5aa0;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .property-table td {\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          max-width: 120px;\r\n        }\r\n\r\n        .property-table a {\r\n          color: #007bff;\r\n          text-decoration: none;\r\n        }\r\n\r\n        .property-table a:hover {\r\n          text-decoration: underline;\r\n        }\r\n\r\n        .property-details {\r\n          padding: 20px;\r\n          border-top: 1px solid #eee;\r\n        }\r\n\r\n        .property-details summary {\r\n          cursor: pointer;\r\n          font-weight: 600;\r\n          color: #2c5aa0;\r\n          margin-bottom: 15px;\r\n        }\r\n\r\n        .details-grid {\r\n          display: grid;\r\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n          gap: 20px;\r\n          margin-top: 15px;\r\n        }\r\n\r\n        .detail-card {\r\n          background: #f8f9fa;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #2c5aa0;\r\n        }\r\n\r\n        .detail-card h4 {\r\n          margin: 0 0 10px 0;\r\n          color: #2c5aa0;\r\n        }\r\n\r\n        .detail-info p {\r\n          margin: 5px 0;\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .detail-info strong {\r\n          color: #333;\r\n        }\r\n\r\n        /* 移动端适配 */\r\n        @media (max-width: 768px) {\r\n          .container {\r\n            padding: 10px;\r\n          }\r\n\r\n          .property-table-container {\r\n            padding: 0 10px;\r\n          }\r\n\r\n          .property-table {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .property-table th,\r\n          .property-table td {\r\n            padding: 8px 4px;\r\n          }\r\n\r\n          .details-grid {\r\n            grid-template-columns: 1fr;\r\n          }\r\n\r\n          .property-meta {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 5px;\r\n          }\r\n\r\n          .search-button {\r\n            width: 100%;\r\n            max-width: 300px;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}", "// 定义数据类型\r\nexport interface PropertyData {\r\n  id: string;\r\n  address: string;\r\n  searchDate: string;\r\n  totalSources: number;\r\n  successfulSources: number;\r\n  properties: PropertyInfo[];\r\n}\r\n\r\nexport interface PropertyInfo {\r\n  siteName: string;\r\n  address: string;\r\n  propertyType: string;\r\n  bedrooms: string;\r\n  bathrooms: string;\r\n  parking: string;\r\n  landSize: string;\r\n  buildingSize: string;\r\n  yearBuilt: string;\r\n  currentGuidePrice: string;\r\n  estimatedValueRange: string;\r\n  estimatedValueMid: string;\r\n  auctionDate: string;\r\n  inspectionTimes: string[];\r\n  historyRecords: any[];\r\n  description: string;\r\n  features: string[];\r\n  contact: string;\r\n  sourceUrl: string;\r\n}\r\n\r\n// 模拟搜索和抓取功能（实际调用将通过API路由处理）\r\nexport async function searchAndScrapeProperty(address: string): Promise<{success: boolean, data?: PropertyData, error?: string}> {\r\n  // 这个函数将在客户端调用API路由来执行实际的搜索和抓取\r\n  try {\r\n    const response = await fetch('/api/search-property', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ address }),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(`API调用失败: ${response.status}`);\r\n    }\r\n    \r\n    const result = await response.json();\r\n    return result;\r\n  } catch (error) {\r\n    console.error('❌ 搜索失败:', error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : String(error)\r\n    };\r\n  }\r\n}\r\n\r\n// 本地存储工具函数\r\nexport const localStorageUtils = {\r\n  saveProperties: (properties: any[]) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('propertySearchResults', JSON.stringify(properties));\r\n    }\r\n  },\r\n  \r\n  loadProperties: (): any[] => {\r\n    if (typeof window !== 'undefined') {\r\n      const saved = localStorage.getItem('propertySearchResults');\r\n      return saved ? JSON.parse(saved) : [];\r\n    }\r\n    return [];\r\n  },\r\n  \r\n  removeProperty: (index: number) => {\r\n    if (typeof window !== 'undefined') {\r\n      const properties = localStorageUtils.loadProperties();\r\n      properties.splice(index, 1);\r\n      localStorageUtils.saveProperties(properties);\r\n      return properties;\r\n    }\r\n    return [];\r\n  }\r\n};\r\n\r\n// 剪贴板工具函数\r\nexport const clipboardUtils = {\r\n  readText: async (): Promise<string> => {\r\n    if (typeof window !== 'undefined' && navigator.clipboard) {\r\n      try {\r\n        return await navigator.clipboard.readText();\r\n      } catch (error) {\r\n        console.error('无法读取剪贴板:', error);\r\n        throw new Error('无法访问剪贴板，请检查浏览器权限');\r\n      }\r\n    }\r\n    throw new Error('剪贴板API不可用');\r\n  }\r\n};", "import { useState, useEffect } from \"react\";\r\nimport { localStorageUtils, clipboardUtils } from \"~/utils/property-search\";\r\nimport type { PropertyData } from \"~/utils/property-search\";\r\n\r\nexport default function Index() {\r\n  const [properties, setProperties] = useState<PropertyData[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 从localStorage加载数据\r\n  useEffect(() => {\r\n    const savedProperties = localStorageUtils.loadProperties();\r\n    setProperties(savedProperties);\r\n  }, []);\r\n\r\n  // 保存到localStorage\r\n  useEffect(() => {\r\n    localStorageUtils.saveProperties(properties);\r\n  }, [properties]);\r\n\r\n  const handleSearchClick = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // 读取剪贴板内容\r\n      const clipboardText = await clipboardUtils.readText();\r\n      \r\n      if (!clipboardText.trim()) {\r\n        throw new Error('剪贴板内容为空');\r\n      }\r\n\r\n      console.log('剪贴板内容:', clipboardText);\r\n\r\n      // 调用API进行搜索和抓取\r\n      const response = await fetch('/api/search-property', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ address: clipboardText.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json().catch(() => ({}));\r\n        throw new Error(errorData.error || `API调用失败: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      \r\n      if (result.success && result.data) {\r\n        // 添加新的房产数据到列表开头\r\n        setProperties(prev => [result.data, ...prev]);\r\n      } else {\r\n        throw new Error(result.error || '搜索失败');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('搜索失败:', error);\r\n      setError(error instanceof Error ? error.message : String(error));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteProperty = (index: number) => {\r\n    setProperties(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <style dangerouslySetInnerHTML={{__html: `\r\n        .container {\r\n          max-width: 1200px;\r\n          margin: 0 auto;\r\n          padding: 20px;\r\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n        }\r\n\r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n\r\n        .header h1 {\r\n          color: #2c5aa0;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .header p {\r\n          color: #666;\r\n          font-size: 16px;\r\n        }\r\n\r\n        .search-section {\r\n          text-align: center;\r\n          margin-bottom: 40px;\r\n        }\r\n\r\n        .search-button {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: white;\r\n          border: none;\r\n          padding: 15px 30px;\r\n          font-size: 18px;\r\n          border-radius: 8px;\r\n          cursor: pointer;\r\n          box-shadow: 0 4px 15px rgba(0,0,0,0.2);\r\n          transition: all 0.3s ease;\r\n          min-width: 200px;\r\n        }\r\n\r\n        .search-button:hover:not(:disabled) {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 6px 20px rgba(0,0,0,0.3);\r\n        }\r\n\r\n        .search-button:disabled {\r\n          opacity: 0.7;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .search-button.loading {\r\n          animation: pulse 1s infinite;\r\n        }\r\n\r\n        @keyframes pulse {\r\n          0% { opacity: 1; }\r\n          50% { opacity: 0.7; }\r\n          100% { opacity: 1; }\r\n        }\r\n\r\n        .error-message {\r\n          background: #fee;\r\n          color: #c33;\r\n          padding: 10px;\r\n          border-radius: 4px;\r\n          margin-top: 10px;\r\n          border: 1px solid #fcc;\r\n        }\r\n\r\n        .results-section h2 {\r\n          color: #2c5aa0;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .property-card {\r\n          background: white;\r\n          border-radius: 8px;\r\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\r\n          margin-bottom: 20px;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .property-header {\r\n          background: #f8f9fa;\r\n          padding: 20px;\r\n          border-bottom: 1px solid #eee;\r\n        }\r\n\r\n        .property-header h3 {\r\n          margin: 0 0 10px 0;\r\n          color: #2c5aa0;\r\n          font-size: 20px;\r\n        }\r\n\r\n        .property-meta {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          font-size: 14px;\r\n          color: #666;\r\n          flex-wrap: wrap;\r\n          gap: 10px;\r\n        }\r\n\r\n        .property-meta span {\r\n          margin-right: 15px;\r\n        }\r\n\r\n        .delete-button {\r\n          background: #dc3545;\r\n          color: white;\r\n          border: none;\r\n          padding: 5px 10px;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n          font-size: 12px;\r\n        }\r\n\r\n        .delete-button:hover {\r\n          background: #c82333;\r\n        }\r\n\r\n        .property-table-container {\r\n          overflow-x: auto;\r\n          padding: 20px;\r\n        }\r\n\r\n        .property-table {\r\n          width: 100%;\r\n          border-collapse: collapse;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .property-table th,\r\n        .property-table td {\r\n          padding: 12px 8px;\r\n          text-align: left;\r\n          border-bottom: 1px solid #eee;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          max-width: 120px;\r\n        }\r\n\r\n        .property-table th {\r\n          background: #f8f9fa;\r\n          font-weight: 600;\r\n          color: #2c5aa0;\r\n        }\r\n\r\n        .property-table a {\r\n          color: #007bff;\r\n          text-decoration: none;\r\n        }\r\n\r\n        .property-table a:hover {\r\n          text-decoration: underline;\r\n        }\r\n\r\n        .property-details {\r\n          padding: 20px;\r\n          border-top: 1px solid #eee;\r\n        }\r\n\r\n        .property-details summary {\r\n          cursor: pointer;\r\n          font-weight: 600;\r\n          color: #2c5aa0;\r\n          margin-bottom: 15px;\r\n        }\r\n\r\n        .details-grid {\r\n          display: grid;\r\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n          gap: 20px;\r\n          margin-top: 15px;\r\n        }\r\n\r\n        .detail-card {\r\n          background: #f8f9fa;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #2c5aa0;\r\n        }\r\n\r\n        .detail-card h4 {\r\n          margin: 0 0 10px 0;\r\n          color: #2c5aa0;\r\n        }\r\n\r\n        .detail-info p {\r\n          margin: 5px 0;\r\n          font-size: 14px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .detail-info strong {\r\n          color: #333;\r\n        }\r\n\r\n        /* 移动端适配 */\r\n        @media (max-width: 768px) {\r\n          .container {\r\n            padding: 10px;\r\n          }\r\n\r\n          .property-table-container {\r\n            padding: 10px;\r\n          }\r\n\r\n          .property-table {\r\n            font-size: 12px;\r\n          }\r\n\r\n          .property-table th,\r\n          .property-table td {\r\n            padding: 8px 4px;\r\n          }\r\n\r\n          .details-grid {\r\n            grid-template-columns: 1fr;\r\n          }\r\n\r\n          .property-meta {\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n          }\r\n\r\n          .search-button {\r\n            width: 100%;\r\n            max-width: 300px;\r\n          }\r\n        }\r\n      `}} />\r\n      \r\n      <div className=\"container\">\r\n        <div className=\"header\">\r\n          <h1>🏠 房产信息搜索</h1>\r\n          <p>点击按钮从剪贴板读取地址并搜索房产信息</p>\r\n        </div>\r\n\r\n        <div className=\"search-section\">\r\n          <button \r\n            className={`search-button ${isLoading ? 'loading' : ''}`}\r\n            onClick={handleSearchClick}\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? '🔍 搜索中...' : '📋 从剪贴板搜索'}\r\n          </button>\r\n          \r\n          {error && (\r\n            <div className=\"error-message\">\r\n              ❌ {error}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {properties.length > 0 && (\r\n          <div className=\"results-section\">\r\n            <h2>搜索结果 ({properties.length})</h2>\r\n            \r\n            {properties.map((property, index) => (\r\n              <div key={property.id} className=\"property-card\">\r\n                <div className=\"property-header\">\r\n                  <h3>{property.address}</h3>\r\n                  <div className=\"property-meta\">\r\n                    <span>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>\r\n                    <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>\r\n                    <button \r\n                      className=\"delete-button\"\r\n                      onClick={() => handleDeleteProperty(index)}\r\n                      title=\"删除这条记录\"\r\n                    >\r\n                      🗑️ 删除\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"property-table-container\">\r\n                  <table className=\"property-table\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th>网站</th>\r\n                        <th>房型</th>\r\n                        <th>卧室</th>\r\n                        <th>浴室</th>\r\n                        <th>停车</th>\r\n                        <th>建成年份</th>\r\n                        <th>指导价</th>\r\n                        <th>估价范围</th>\r\n                        <th>拍卖日期</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {property.properties.map((prop, propIndex) => (\r\n                        <tr key={propIndex}>\r\n                          <td>\r\n                            <a href={prop.sourceUrl} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                              {prop.siteName}\r\n                            </a>\r\n                          </td>\r\n                          <td>{prop.propertyType}</td>\r\n                          <td>{prop.bedrooms}</td>\r\n                          <td>{prop.bathrooms}</td>\r\n                          <td>{prop.parking}</td>\r\n                          <td>{prop.yearBuilt}</td>\r\n                          <td>{prop.currentGuidePrice}</td>\r\n                          <td>{prop.estimatedValueRange}</td>\r\n                          <td>{prop.auctionDate}</td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n\r\n                {/* 详细信息折叠区域 */}\r\n                <details className=\"property-details\">\r\n                  <summary>查看详细信息</summary>\r\n                  <div className=\"details-grid\">\r\n                    {property.properties.map((prop, propIndex) => (\r\n                      <div key={propIndex} className=\"detail-card\">\r\n                        <h4>{prop.siteName}</h4>\r\n                        <div className=\"detail-info\">\r\n                          <p><strong>地址:</strong> {prop.address}</p>\r\n                          <p><strong>土地面积:</strong> {prop.landSize}</p>\r\n                          <p><strong>建筑面积:</strong> {prop.buildingSize}</p>\r\n                          <p><strong>估价中位数:</strong> {prop.estimatedValueMid}</p>\r\n                          {prop.features.length > 0 && (\r\n                            <p><strong>特色:</strong> {prop.features.join(', ')}</p>\r\n                          )}\r\n                          {prop.inspectionTimes.length > 0 && (\r\n                            <p><strong>开放检查:</strong> {prop.inspectionTimes.join(', ')}</p>\r\n                          )}\r\n                          {prop.contact !== 'N/A' && (\r\n                            <p><strong>联系方式:</strong> {prop.contact}</p>\r\n                          )}\r\n                          {prop.description !== 'N/A' && (\r\n                            <p><strong>描述:</strong> {prop.description}</p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </details>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}", "import { useState, useEffect } from \"react\";\r\nimport { localStorageUtils, clipboardUtils, type PropertyData } from \"~/utils/property-search\";\r\n\r\nexport default function Index() {\r\n  const [properties, setProperties] = useState<PropertyData[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 从localStorage加载数据\r\n  useEffect(() => {\r\n    const savedProperties = localStorageUtils.loadProperties();\r\n    setProperties(savedProperties);\r\n  }, []);\r\n\r\n  // 保存到localStorage\r\n  useEffect(() => {\r\n    localStorageUtils.saveProperties(properties);\r\n  }, [properties]);\r\n\r\n  const handleSearchClick = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // 读取剪贴板内容\r\n      const clipboardText = await clipboardUtils.readText();\r\n      \r\n      if (!clipboardText.trim()) {\r\n        throw new Error('剪贴板内容为空');\r\n      }\r\n\r\n      console.log('剪贴板内容:', clipboardText);\r\n\r\n      // 调用API进行搜索和抓取\r\n      const response = await fetch('/api/search-property', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ address: clipboardText.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.error || `API调用失败: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      \r\n      if (result.success && result.data) {\r\n        // 添加新的房产数据到列表开头\r\n        setProperties(prev => [result.data, ...prev]);\r\n      } else {\r\n        throw new Error(result.error || '搜索失败');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('搜索失败:', error);\r\n      setError(error instanceof Error ? error.message : String(error));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteProperty = (index: number) => {\r\n    setProperties(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const buttonStyle = {\r\n    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    color: 'white',\r\n    border: 'none',\r\n    padding: '15px 30px',\r\n    fontSize: '18px',\r\n    borderRadius: '8px',\r\n    cursor: isLoading ? 'not-allowed' : 'pointer',\r\n    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\r\n    minWidth: '200px',\r\n    opacity: isLoading ? 0.7 : 1,\r\n  };\r\n\r\n  const containerStyle = {\r\n    maxWidth: '1200px',\r\n    margin: '0 auto',\r\n    padding: '20px',\r\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\r\n  };\r\n\r\n  const errorStyle = {\r\n    background: '#fee',\r\n    color: '#c33',\r\n    padding: '10px',\r\n    borderRadius: '4px',\r\n    marginTop: '10px',\r\n    border: '1px solid #fcc',\r\n  };\r\n\r\n  const cardStyle = {\r\n    background: 'white',\r\n    borderRadius: '8px',\r\n    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\r\n    marginBottom: '20px',\r\n    overflow: 'hidden' as const,\r\n  };\r\n\r\n  const headerStyle = {\r\n    background: '#f8f9fa',\r\n    padding: '20px',\r\n    borderBottom: '1px solid #eee',\r\n  };\r\n\r\n  const tableStyle = {\r\n    width: '100%',\r\n    borderCollapse: 'collapse' as const,\r\n    margin: '20px 0',\r\n    fontSize: '14px',\r\n  };\r\n\r\n  const thStyle = {\r\n    padding: '12px 8px',\r\n    textAlign: 'left' as const,\r\n    borderBottom: '1px solid #eee',\r\n    background: '#f8f9fa',\r\n    fontWeight: 600,\r\n    color: '#2c5aa0',\r\n  };\r\n\r\n  const tdStyle = {\r\n    padding: '12px 8px',\r\n    textAlign: 'left' as const,\r\n    borderBottom: '1px solid #eee',\r\n    maxWidth: '120px',\r\n    overflow: 'hidden' as const,\r\n    textOverflow: 'ellipsis',\r\n  };\r\n\r\n  return (\r\n    <div style={containerStyle}>\r\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\r\n        <h1 style={{ color: '#2c5aa0', marginBottom: '10px' }}>🏠 房产信息搜索</h1>\r\n        <p style={{ color: '#666', fontSize: '16px' }}>点击按钮从剪贴板读取地址并搜索房产信息</p>\r\n      </div>\r\n\r\n      <div style={{ textAlign: 'center', marginBottom: '40px' }}>\r\n        <button \r\n          style={buttonStyle}\r\n          onClick={handleSearchClick}\r\n          disabled={isLoading}\r\n        >\r\n          {isLoading ? '🔍 搜索中...' : '📋 从剪贴板搜索'}\r\n        </button>\r\n        \r\n        {error && (\r\n          <div style={errorStyle}>\r\n            ❌ {error}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {properties.length > 0 && (\r\n        <div>\r\n          <h2 style={{ color: '#2c5aa0', marginBottom: '20px' }}>搜索结果 ({properties.length})</h2>\r\n          \r\n          {properties.map((property, index) => (\r\n            <div key={property.id} style={cardStyle}>\r\n              <div style={headerStyle}>\r\n                <h3 style={{ margin: '0 0 10px 0', color: '#2c5aa0', fontSize: '20px' }}>{property.address}</h3>\r\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '14px', color: '#666' }}>\r\n                  <div>\r\n                    <span style={{ marginRight: '15px' }}>🗓️ {new Date(property.searchDate).toLocaleString('zh-CN')}</span>\r\n                    <span>📊 {property.successfulSources}/{property.totalSources} 数据源</span>\r\n                  </div>\r\n                  <button \r\n                    style={{\r\n                      background: '#dc3545',\r\n                      color: 'white',\r\n                      border: 'none',\r\n                      padding: '5px 10px',\r\n                      borderRadius: '4px',\r\n                      cursor: 'pointer',\r\n                      fontSize: '12px'\r\n                    }}\r\n                    onClick={() => handleDeleteProperty(index)}\r\n                    title=\"删除这条记录\"\r\n                  >\r\n                    🗑️\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <div style={{ overflowX: 'auto', padding: '0 20px' }}>\r\n                <table style={tableStyle}>\r\n                  <thead>\r\n                    <tr>\r\n                      <th style={thStyle}>网站</th>\r\n                      <th style={thStyle}>房型</th>\r\n                      <th style={thStyle}>卧室</th>\r\n                      <th style={thStyle}>浴室</th>\r\n                      <th style={thStyle}>停车</th>\r\n                      <th style={thStyle}>建成年份</th>\r\n                      <th style={thStyle}>指导价</th>\r\n                      <th style={thStyle}>估价范围</th>\r\n                      <th style={thStyle}>拍卖日期</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {property.properties.map((prop, propIndex) => (\r\n                      <tr key={propIndex}>\r\n                        <td style={tdStyle}>\r\n                          <a href={prop.sourceUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{ color: '#007bff', textDecoration: 'none' }}>\r\n                            {prop.siteName}\r\n                          </a>\r\n                        </td>\r\n                        <td style={tdStyle}>{prop.propertyType}</td>\r\n                        <td style={tdStyle}>{prop.bedrooms}</td>\r\n                        <td style={tdStyle}>{prop.bathrooms}</td>\r\n                        <td style={tdStyle}>{prop.parking}</td>\r\n                        <td style={tdStyle}>{prop.yearBuilt}</td>\r\n                        <td style={tdStyle}>{prop.currentGuidePrice}</td>\r\n                        <td style={tdStyle}>{prop.estimatedValueRange}</td>\r\n                        <td style={tdStyle}>{prop.auctionDate}</td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n\r\n              <details style={{ padding: '20px', borderTop: '1px solid #eee' }}>\r\n                <summary style={{ cursor: 'pointer', fontWeight: 600, color: '#2c5aa0', marginBottom: '15px' }}>\r\n                  查看详细信息\r\n                </summary>\r\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginTop: '15px' }}>\r\n                  {property.properties.map((prop, propIndex) => (\r\n                    <div key={propIndex} style={{ \r\n                      background: '#f8f9fa', \r\n                      padding: '15px', \r\n                      borderRadius: '6px', \r\n                      borderLeft: '4px solid #2c5aa0' \r\n                    }}>\r\n                      <h4 style={{ margin: '0 0 10px 0', color: '#2c5aa0' }}>{prop.siteName}</h4>\r\n                      <div>\r\n                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                          <strong style={{ color: '#333' }}>地址:</strong> {prop.address}\r\n                        </p>\r\n                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                          <strong style={{ color: '#333' }}>土地面积:</strong> {prop.landSize}\r\n                        </p>\r\n                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                          <strong style={{ color: '#333' }}>建筑面积:</strong> {prop.buildingSize}\r\n                        </p>\r\n                        <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                          <strong style={{ color: '#333' }}>估价中位数:</strong> {prop.estimatedValueMid}\r\n                        </p>\r\n                        {prop.features.length > 0 && (\r\n                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                            <strong style={{ color: '#333' }}>特色:</strong> {prop.features.join(', ')}\r\n                          </p>\r\n                        )}\r\n                        {prop.inspectionTimes.length > 0 && (\r\n                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                            <strong style={{ color: '#333' }}>开放检查:</strong> {prop.inspectionTimes.join(', ')}\r\n                          </p>\r\n                        )}\r\n                        {prop.contact !== 'N/A' && (\r\n                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                            <strong style={{ color: '#333' }}>联系方式:</strong> {prop.contact}\r\n                          </p>\r\n                        )}\r\n                        {prop.description !== 'N/A' && (\r\n                          <p style={{ margin: '5px 0', fontSize: '14px', lineHeight: '1.4' }}>\r\n                            <strong style={{ color: '#333' }}>描述:</strong> {prop.description}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </details>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}", "export default {'entry':{'module':'/build/entry.client-BPYWITAV.js','imports':['/build/_shared/chunk-O4BRYNJ4.js','/build/_shared/chunk-V3BJQ67B.js','/build/_shared/chunk-U4FRFQSK.js','/build/_shared/chunk-X3T7OMQU.js','/build/_shared/chunk-XGOTYLZ5.js','/build/_shared/chunk-7M6SC7J5.js','/build/_shared/chunk-UWV35TSL.js','/build/_shared/chunk-PNG5AS42.js']},'routes':{'root':{'id':'root','parentId':undefined,'path':'','index':undefined,'caseSensitive':undefined,'module':'/build/root-BKU7K54G.js','imports':undefined,'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/_index':{'id':'routes/_index','parentId':'root','path':undefined,'index':true,'caseSensitive':undefined,'module':'/build/routes/_index-GZX4PWJK.js','imports':['/build/_shared/chunk-MNIL4YD6.js'],'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/_index-old':{'id':'routes/_index-old','parentId':'root','path':undefined,'index':undefined,'caseSensitive':undefined,'module':'/build/routes/_index-old-NSHHUIEC.js','imports':['/build/_shared/chunk-MNIL4YD6.js'],'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/_index_new':{'id':'routes/_index_new','parentId':'root','path':undefined,'index':undefined,'caseSensitive':undefined,'module':'/build/routes/_index_new-WFDJIEW5.js','imports':['/build/_shared/chunk-MNIL4YD6.js'],'hasAction':false,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false},'routes/api.search-property':{'id':'routes/api.search-property','parentId':'root','path':'api/search-property','index':undefined,'caseSensitive':undefined,'module':'/build/routes/api.search-property-JHHPWYLI.js','imports':undefined,'hasAction':true,'hasLoader':false,'hasClientAction':false,'hasClientLoader':false,'hasErrorBoundary':false}},'version':'81fde8d0','hmr':{'runtime':'/build/_shared\\\\chunk-X3T7OMQU.js','timestamp':1758896614663},'url':'/build/manifest-81FDE8D0.js'};", "\nimport * as entryServer from \"C:\\\\Users\\\\<USER>\\\\projects\\\\property_helper\\\\app\\\\entry.server.tsx\";\nimport * as route0 from \"./root.tsx\";\nimport * as route1 from \"./routes/api.search-property.ts\";\nimport * as route2 from \"./routes/_index-old.tsx\";\nimport * as route3 from \"./routes/_index_new.tsx\";\nimport * as route4 from \"./routes/_index.tsx\";\n  export const mode = \"development\";\n  export { default as assets } from \"@remix-run/dev/assets-manifest\";\n  export const assetsBuildDirectory = \"public\\\\build\";\n  export const future = {\"v3_fetcherPersist\":false,\"v3_relativeSplatPath\":false,\"v3_throwAbortReason\":false,\"v3_routeConfig\":false,\"v3_singleFetch\":false,\"v3_lazyRouteDiscovery\":false,\"unstable_optimizeDeps\":false};\n  export const publicPath = \"/build/\";\n  export const entry = { module: entryServer };\n  export const routes = {\n    \"root\": {\n      id: \"root\",\n      parentId: undefined,\n      path: \"\",\n      index: undefined,\n      caseSensitive: undefined,\n      module: route0\n    },\n  \"routes/api.search-property\": {\n      id: \"routes/api.search-property\",\n      parentId: \"root\",\n      path: \"api/search-property\",\n      index: undefined,\n      caseSensitive: undefined,\n      module: route1\n    },\n  \"routes/_index-old\": {\n      id: \"routes/_index-old\",\n      parentId: \"root\",\n      path: undefined,\n      index: undefined,\n      caseSensitive: undefined,\n      module: route2\n    },\n  \"routes/_index_new\": {\n      id: \"routes/_index_new\",\n      parentId: \"root\",\n      path: undefined,\n      index: undefined,\n      caseSensitive: undefined,\n      module: route3\n    },\n  \"routes/_index\": {\n      id: \"routes/_index\",\n      parentId: \"root\",\n      path: undefined,\n      index: true,\n      caseSensitive: undefined,\n      module: route4\n    }\n  };"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AACA,SAAS,mBAAmB;AAC5B,SAAS,sBAAsB;AAS3B;AAPW,SAAR,cACL,SACA,oBACA,iBACA,cACA;AACA,MAAM,SAAS;AAAA,IACb,uBAAC,eAAY,SAAS,cAAc,KAAK,QAAQ,OAAjD;AAAA;AAAA;AAAA;AAAA,WAAsD;AAAA,EACxD;AAEA,yBAAgB,IAAI,gBAAgB,WAAW,GAExC,IAAI,SAAS,oBAAoB,QAAQ;AAAA,IAC9C,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACH;;;ACpBA;AAAA;AAAA;AAAA;AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAMC,mBAAAA,eAAA;AAJO,SAAR,MAAuB;AAC5B,SACE,gBAAAA,QAAC,UAAK,MAAK,SACT;AAAA,oBAAAA,QAAC,UACC;AAAA,sBAAAA,QAAC,UAAK,SAAQ,WAAd;AAAA;AAAA;AAAA;AAAA,aAAsB;AAAA,MACtB,gBAAAA,QAAC,UAAK,MAAK,YAAW,SAAQ,yCAA9B;AAAA;AAAA;AAAA;AAAA,aAAoE;AAAA,MACpE,gBAAAA,QAAC,UAAD;AAAA;AAAA;AAAA;AAAA,aAAM;AAAA,MACN,gBAAAA,QAAC,WAAD;AAAA;AAAA;AAAA;AAAA,aAAO;AAAA,MACP,gBAAAA,QAAC,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAR;AAAA;AAAA;AAAA;AAAA,aAwBE;AAAA,SA7BJ;AAAA;AAAA;AAAA;AAAA,WA8BA;AAAA,IACA,gBAAAA,QAAC,UACC;AAAA,sBAAAA,QAAC,YAAD;AAAA;AAAA;AAAA;AAAA,aAAQ;AAAA,MACR,gBAAAA,QAAC,uBAAD;AAAA;AAAA;AAAA;AAAA,aAAmB;AAAA,MACnB,gBAAAA,QAAC,aAAD;AAAA;AAAA;AAAA;AAAA,aAAS;AAAA,MACT,gBAAAA,QAAC,gBAAD;AAAA;AAAA;AAAA;AAAA,aAAY;AAAA,SAJd;AAAA;AAAA;AAAA;AAAA,WAKA;AAAA,OArCF;AAAA;AAAA;AAAA;AAAA,SAsCA;AAEJ;;;ACnDA;AAAA;AAAA;AAAA;AAAA,SAAS,YAAY;;;ACCrB,OAAO,SAAS;AAGhB,SAAS,4BAA4B;AAEnC,MAAM,UADe,CAAC,aAAa,EACN,OAAO,aAAW,CAAC,QAAQ,IAAI,OAAO,CAAC;AAEpE,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,MAAM,2DAAc,QAAQ,KAAK,IAAI,uFAAsB;AAEzE;AAGA,eAAe,mBAAmB,SAAiC;AACjE,MAAI;AACF,YAAQ,IAAI,8BAAa,SAAS;AAElC,QAAM,MAAM,IAAI,IAAI,QAAQ,IAAI,WAAW,GAErC,cAAc,GAAG;AACvB,YAAQ,IAAI,uCAAY,aAAa;AAErC,QAAM,SAAS,MAAM,IAAI,OAAO,aAAa;AAAA,MAC3C,MAAM;AAAA,MACN,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAED,mBAAQ,IAAI,wDAAgB,OAAO,QAAQ,2BAAY,GAChD,OAAO;AAAA,EAChB,SAAS,OAAP;AACA,kBAAQ,MAAM,uCAAc,KAAK,GAC3B;AAAA,EACR;AACF;AAGA,SAAS,oBAAoB,eAAsB,eAA8B;AAC/E,SAAO,cAAc,IAAI,CAAC,QAAQ,UAAU;AAE1C,QAAM,OAAO,OAAO,QAAQ,IACtB,QAAQ,OAAO,SAAS,IAGxB,gBAAgB,KAAK,MAAM,2BAA2B,GACtD,iBAAiB,KAAK,MAAM,6BAA6B,GACzD,aAAa,KAAK,MAAM,WAAW;AAEzC,WAAO;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ;AAAA,MACA,KAAK,OAAO;AAAA,MACZ,UAAU,IAAI,IAAI,OAAO,GAAG,EAAE;AAAA,MAC9B,SAAS,MAAM,SAAS,aAAa,IAAI,gBAAgB;AAAA,MACzD,UAAU,gBAAgB,cAAc,CAAC,IAAI;AAAA,MAC7C,WAAW,iBAAiB,eAAe,CAAC,IAAI;AAAA,MAChD,WAAW,aAAa,WAAW,CAAC,IAAI;AAAA,MACxC,aAAa,KAAK,UAAU,GAAG,GAAG,IAAI;AAAA,MACtC,WAAW,OAAO;AAAA,MAClB,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,IACtC;AAAA,EACF,CAAC;AACH;AAGA,eAAsB,sBAAsB,SAAiB;AAC3D,MAAI;AACF,YAAQ,IAAI,mFAAqB,SAAS,GAG1C,0BAA0B,GAG1B,QAAQ,IAAI,qEAAsB;AAClC,QAAM,gBAAgB,MAAM,mBAAmB,OAAO;AAEtD,QAAI,CAAC,iBAAiB,cAAc,WAAW;AAC7C,YAAM,IAAI,MAAM,2DAAc;AAIhC,YAAQ,IAAI,0FAAuB;AACnC,QAAM,gBAAgB,oBAAoB,eAAe,OAAO,GAG1D,cAAc;AAAA,MAClB,IAAI,YAAY,KAAK,IAAI;AAAA,MACzB;AAAA,MACA,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,MACnC,cAAc,cAAc;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,aAAa,GAAG;AAAA,QAChB,SAAS,CAAC,iBAAiB,qBAAqB,eAAe,aAAa;AAAA,QAC5E,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACtC;AAAA,IACF;AAEA,mBAAQ,IAAI,iEAAe,GACpB;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EAEF,SAAS,OAAP;AACA,mBAAQ,MAAM,oEAAkB,KAAK,GAC9B;AAAA,MACL,SAAS;AAAA,MACT,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,IAC9D;AAAA,EACF;AACF;;;ADpHA,eAAsB,OAAO,EAAE,QAAQ,GAAuB;AAC5D,MAAI,QAAQ,WAAW;AACrB,WAAO,KAAK,EAAE,OAAO,qBAAqB,GAAG,EAAE,QAAQ,IAAI,CAAC;AAG9D,MAAI;AACF,QAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAEvC,QAAI,CAAC,WAAW,OAAO,WAAY;AACjC,aAAO,KAAK,EAAE,OAAO,2BAA2B,GAAG,EAAE,QAAQ,IAAI,CAAC;AAGpE,YAAQ,IAAI,oEAAqB,SAAS;AAG1C,QAAM,SAAS,MAAM,sBAAsB,OAAO;AAElD,WAAI,OAAO,UACF,KAAK,MAAM,IAEX,KAAK,QAAQ,EAAE,QAAQ,IAAI,CAAC;AAAA,EAGvC,SAAS,OAAP;AACA,mBAAQ,MAAM,qDAAkB,KAAK,GAC9B,KAAK;AAAA,MACV,SAAS;AAAA,MACT,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,IAC9D,GAAG,EAAE,QAAQ,IAAI,CAAC;AAAA,EACpB;AACF;;;AElCA;AAAA;AAAA;AAAA;AAAA,SAAS,UAAU,iBAAiB;;;AC4D7B,IAAM,oBAAoB;AAAA,EAC/B,gBAAgB,CAAC,eAAsB;AACrC,IAAI,OAAO,SAAW,OACpB,aAAa,QAAQ,yBAAyB,KAAK,UAAU,UAAU,CAAC;AAAA,EAE5E;AAAA,EAEA,gBAAgB,MAAa;AAC3B,QAAI,OAAO,SAAW,KAAa;AACjC,UAAM,QAAQ,aAAa,QAAQ,uBAAuB;AAC1D,aAAO,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA;AAEtC,WAAO,CAAC;AAAA,EACV;AAAA,EAEA,gBAAgB,CAAC,UAAkB;AACjC,QAAI,OAAO,SAAW,KAAa;AACjC,UAAM,aAAa,kBAAkB,eAAe;AACpD,wBAAW,OAAO,OAAO,CAAC,GAC1B,kBAAkB,eAAe,UAAU,GACpC;AAAA;AAET,WAAO,CAAC;AAAA,EACV;AACF,GAGa,iBAAiB;AAAA,EAC5B,UAAU,YAA6B;AACrC,QAAI,OAAO,SAAW,OAAe,UAAU;AAC7C,UAAI;AACF,eAAO,MAAM,UAAU,UAAU,SAAS;AAAA,MAC5C,SAAS,OAAP;AACA,sBAAQ,MAAM,+CAAY,KAAK,GACzB,IAAI,MAAM,kGAAkB;AAAA,MACpC;AAEF,UAAM,IAAI,MAAM,yCAAW;AAAA,EAC7B;AACF;;;AD5BQ,mBAAAC,eAAA;AApEO,SAAR,QAAyB;AAC9B,MAAM,CAAC,YAAY,aAAa,IAAI,SAAyB,CAAC,CAAC,GACzD,CAAC,WAAW,YAAY,IAAI,SAAS,EAAK,GAC1C,CAAC,OAAO,QAAQ,IAAI,SAAwB,IAAI;AAGtD,YAAU,MAAM;AACd,QAAM,kBAAkB,kBAAkB,eAAe;AACzD,kBAAc,eAAe;AAAA,EAC/B,GAAG,CAAC,CAAC,GAGL,UAAU,MAAM;AACd,sBAAkB,eAAe,UAAU;AAAA,EAC7C,GAAG,CAAC,UAAU,CAAC;AAEf,MAAM,oBAAoB,YAAY;AACpC,iBAAa,EAAI,GACjB,SAAS,IAAI;AAEb,QAAI;AAEF,UAAM,gBAAgB,MAAM,eAAe,SAAS;AAEpD,UAAI,CAAC,cAAc,KAAK;AACtB,cAAM,IAAI,MAAM,4CAAS;AAG3B,cAAQ,IAAI,mCAAU,aAAa;AAGnC,UAAM,WAAW,MAAM,MAAM,wBAAwB;AAAA,QACnD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,SAAS,cAAc,KAAK,EAAE,CAAC;AAAA,MACxD,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,cAAM,IAAI,MAAM,UAAU,SAAS,gCAAY,SAAS,QAAQ;AAAA;AAGlE,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,UAAI,OAAO,WAAW,OAAO;AAE3B,sBAAc,UAAQ,CAAC,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA;AAE5C,cAAM,IAAI,MAAM,OAAO,SAAS,0BAAM;AAAA,IAG1C,SAASC,QAAP;AACA,cAAQ,MAAM,6BAASA,MAAK,GAC5B,SAASA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK,CAAC;AAAA,IACjE,UAAE;AACA,mBAAa,EAAK;AAAA,IACpB;AAAA,EACF,GAEM,uBAAuB,CAAC,UAAkB;AAC9C,kBAAc,UAAQ,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAC1D;AAEA,SACE,gBAAAD,QAAC,SAAI,WAAU,aACb;AAAA,oBAAAA,QAAC,SAAI,WAAU,UACb;AAAA,sBAAAA,QAAC,QAAG,8DAAJ;AAAA;AAAA;AAAA;AAAA,aAAa;AAAA,MACb,gBAAAA,QAAC,OAAE,kIAAH;AAAA;AAAA;AAAA;AAAA,aAAsB;AAAA,SAFxB;AAAA;AAAA;AAAA;AAAA,WAGA;AAAA,IAEA,gBAAAA,QAAC,SAAI,WAAU,kBACb;AAAA,sBAAAA;AAAA,QAAC;AAAA;AAAA,UACC,WAAW,iBAAiB,YAAY,YAAY;AAAA,UACpD,SAAS;AAAA,UACT,UAAU;AAAA,UAET,sBAAY,oCAAc;AAAA;AAAA,QAL7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,MAEC,SACC,gBAAAA,QAAC,SAAI,WAAU,iBAAgB;AAAA;AAAA,QAC1B;AAAA,WADL;AAAA;AAAA;AAAA;AAAA,aAEA;AAAA,SAZJ;AAAA;AAAA;AAAA;AAAA,WAcA;AAAA,IAEC,WAAW,SAAS,KACnB,gBAAAA,QAAC,SAAI,WAAU,mBACb;AAAA,sBAAAA,QAAC,QAAG;AAAA;AAAA,QAAO,WAAW;AAAA,QAAO;AAAA,WAA7B;AAAA;AAAA;AAAA;AAAA,aAA8B;AAAA,MAE7B,WAAW,IAAI,CAAC,UAAU,UACzB,gBAAAA,QAAC,SAAsB,WAAU,iBAC/B;AAAA,wBAAAA,QAAC,SAAI,WAAU,mBACb;AAAA,0BAAAA,QAAC,QAAI,mBAAS,WAAd;AAAA;AAAA;AAAA;AAAA,iBAAsB;AAAA,UACtB,gBAAAA,QAAC,SAAI,WAAU,iBACb;AAAA,4BAAAA,QAAC,UAAK;AAAA;AAAA,cAAK,IAAI,KAAK,SAAS,UAAU,EAAE,eAAe,OAAO;AAAA,iBAA/D;AAAA;AAAA;AAAA;AAAA,mBAAiE;AAAA,YACjE,gBAAAA,QAAC,UAAK;AAAA;AAAA,cAAI,SAAS;AAAA,cAAkB;AAAA,cAAE,SAAS;AAAA,cAAa;AAAA,iBAA7D;AAAA;AAAA;AAAA;AAAA,mBAAiE;AAAA,YACjE,gBAAAA;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS,MAAM,qBAAqB,KAAK;AAAA,gBACzC,OAAM;AAAA,gBACP;AAAA;AAAA,cAJD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMA;AAAA,eATF;AAAA;AAAA;AAAA;AAAA,iBAUA;AAAA,aAZF;AAAA;AAAA;AAAA;AAAA,eAaA;AAAA,QAEA,gBAAAA,QAAC,SAAI,WAAU,4BACb,0BAAAA,QAAC,WAAM,WAAU,kBACf;AAAA,0BAAAA,QAAC,WACC,0BAAAA,QAAC,QACC;AAAA,4BAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,mBAAM;AAAA,YACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,mBAAM;AAAA,YACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,mBAAM;AAAA,YACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,mBAAM;AAAA,YACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,mBAAM;AAAA,YACN,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,mBAAQ;AAAA,YACR,gBAAAA,QAAC,QAAG,kCAAJ;AAAA;AAAA;AAAA;AAAA,mBAAO;AAAA,YACP,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,mBAAQ;AAAA,YACR,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,mBAAQ;AAAA,eATV;AAAA;AAAA;AAAA;AAAA,iBAUA,KAXF;AAAA;AAAA;AAAA;AAAA,iBAYA;AAAA,UACA,gBAAAA,QAAC,WACE,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,QACC;AAAA,4BAAAA,QAAC,QACC,0BAAAA,QAAC,OAAE,MAAM,KAAK,WAAW,QAAO,UAAS,KAAI,uBAC1C,eAAK,YADR;AAAA;AAAA;AAAA;AAAA,mBAEA,KAHF;AAAA;AAAA;AAAA;AAAA,mBAIA;AAAA,YACA,gBAAAA,QAAC,QAAI,eAAK,gBAAV;AAAA;AAAA;AAAA;AAAA,mBAAuB;AAAA,YACvB,gBAAAA,QAAC,QAAI,eAAK,YAAV;AAAA;AAAA;AAAA;AAAA,mBAAmB;AAAA,YACnB,gBAAAA,QAAC,QAAI,eAAK,aAAV;AAAA;AAAA;AAAA;AAAA,mBAAoB;AAAA,YACpB,gBAAAA,QAAC,QAAI,eAAK,WAAV;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAAA,YAClB,gBAAAA,QAAC,QAAI,eAAK,aAAV;AAAA;AAAA;AAAA;AAAA,mBAAoB;AAAA,YACpB,gBAAAA,QAAC,QAAI,eAAK,qBAAV;AAAA;AAAA;AAAA;AAAA,mBAA4B;AAAA,YAC5B,gBAAAA,QAAC,QAAI,eAAK,uBAAV;AAAA;AAAA;AAAA;AAAA,mBAA8B;AAAA,YAC9B,gBAAAA,QAAC,QAAI,eAAK,eAAV;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,eAbf,WAAT;AAAA;AAAA;AAAA;AAAA,iBAcA,CACD,KAjBH;AAAA;AAAA;AAAA;AAAA,iBAkBA;AAAA,aAhCF;AAAA;AAAA;AAAA;AAAA,eAiCA,KAlCF;AAAA;AAAA;AAAA;AAAA,eAmCA;AAAA,QAGA,gBAAAA,QAAC,aAAQ,WAAU,oBACjB;AAAA,0BAAAA,QAAC,aAAQ,oDAAT;AAAA;AAAA;AAAA;AAAA,iBAAe;AAAA,UACf,gBAAAA,QAAC,SAAI,WAAU,gBACZ,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,SAAoB,WAAU,eAC7B;AAAA,4BAAAA,QAAC,QAAI,eAAK,YAAV;AAAA;AAAA;AAAA;AAAA,mBAAmB;AAAA,YACnB,gBAAAA,QAAC,SAAI,WAAU,eACb;AAAA,8BAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,uBAAW;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAA9B;AAAA;AAAA;AAAA;AAAA,qBAAsC;AAAA,cACtC,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,uBAAa;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAAhC;AAAA;AAAA;AAAA;AAAA,qBAAyC;AAAA,cACzC,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,uBAAa;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAAhC;AAAA;AAAA;AAAA;AAAA,qBAA6C;AAAA,cAC7C,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,+CAAR;AAAA;AAAA;AAAA;AAAA,uBAAc;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAAjC;AAAA;AAAA;AAAA;AAAA,qBAAmD;AAAA,cAClD,KAAK,SAAS,SAAS,KACtB,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,uBAAW;AAAA,gBAAS;AAAA,gBAAE,KAAK,SAAS,KAAK,IAAI;AAAA,mBAAhD;AAAA;AAAA;AAAA;AAAA,qBAAkD;AAAA,cAEnD,KAAK,gBAAgB,SAAS,KAC7B,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,uBAAa;AAAA,gBAAS;AAAA,gBAAE,KAAK,gBAAgB,KAAK,IAAI;AAAA,mBAAzD;AAAA;AAAA;AAAA;AAAA,qBAA2D;AAAA,cAE5D,KAAK,YAAY,SAChB,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,uBAAa;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAAhC;AAAA;AAAA;AAAA;AAAA,qBAAwC;AAAA,cAEzC,KAAK,gBAAgB,SACpB,gBAAAA,QAAC,OAAE;AAAA,gCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,uBAAW;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAA9B;AAAA;AAAA;AAAA;AAAA,qBAA0C;AAAA,iBAf9C;AAAA;AAAA;AAAA;AAAA,mBAiBA;AAAA,eAnBQ,WAAV;AAAA;AAAA;AAAA;AAAA,iBAoBA,CACD,KAvBH;AAAA;AAAA;AAAA;AAAA,iBAwBA;AAAA,aA1BF;AAAA;AAAA;AAAA;AAAA,eA2BA;AAAA,WAjFQ,SAAS,IAAnB;AAAA;AAAA;AAAA;AAAA,aAkFA,CACD;AAAA,SAvFH;AAAA;AAAA;AAAA;AAAA,WAwFA;AAAA,IAGF,gBAAAA,QAAC,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAR;AAAA;AAAA;AAAA;AAAA,WA8OE;AAAA,OAhWJ;AAAA;AAAA;AAAA;AAAA,SAiWA;AAEJ;;;AExaA;AAAA;AAAA,iBAAAE;AAAA;AAAA,SAAS,YAAAC,WAAU,aAAAC,kBAAiB;AAuE9B,mBAAAC,eAAA;AAnES,SAARC,SAAyB;AAC9B,MAAM,CAAC,YAAY,aAAa,IAAIC,UAAyB,CAAC,CAAC,GACzD,CAAC,WAAW,YAAY,IAAIA,UAAS,EAAK,GAC1C,CAAC,OAAO,QAAQ,IAAIA,UAAwB,IAAI;AAGtD,EAAAC,WAAU,MAAM;AACd,QAAM,kBAAkB,kBAAkB,eAAe;AACzD,kBAAc,eAAe;AAAA,EAC/B,GAAG,CAAC,CAAC,GAGLA,WAAU,MAAM;AACd,sBAAkB,eAAe,UAAU;AAAA,EAC7C,GAAG,CAAC,UAAU,CAAC;AAEf,MAAM,oBAAoB,YAAY;AACpC,iBAAa,EAAI,GACjB,SAAS,IAAI;AAEb,QAAI;AAEF,UAAM,gBAAgB,MAAM,eAAe,SAAS;AAEpD,UAAI,CAAC,cAAc,KAAK;AACtB,cAAM,IAAI,MAAM,4CAAS;AAG3B,cAAQ,IAAI,mCAAU,aAAa;AAGnC,UAAM,WAAW,MAAM,MAAM,wBAAwB;AAAA,QACnD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,SAAS,cAAc,KAAK,EAAE,CAAC;AAAA,MACxD,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAM,YAAY,MAAM,SAAS,KAAK,EAAE,MAAM,OAAO,CAAC,EAAE;AACxD,cAAM,IAAI,MAAM,UAAU,SAAS,gCAAY,SAAS,QAAQ;AAAA;AAGlE,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,UAAI,OAAO,WAAW,OAAO;AAE3B,sBAAc,UAAQ,CAAC,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA;AAE5C,cAAM,IAAI,MAAM,OAAO,SAAS,0BAAM;AAAA,IAG1C,SAASC,QAAP;AACA,cAAQ,MAAM,6BAASA,MAAK,GAC5B,SAASA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK,CAAC;AAAA,IACjE,UAAE;AACA,mBAAa,EAAK;AAAA,IACpB;AAAA,EACF,GAEM,uBAAuB,CAAC,UAAkB;AAC9C,kBAAc,UAAQ,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAC1D;AAEA,SACE,gBAAAJ,QAAC,SACC;AAAA,oBAAAA,QAAC,WAAM,yBAAyB,EAAC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA0OxC,KA1OD;AAAA;AAAA;AAAA;AAAA,WA0OI;AAAA,IAEJ,gBAAAA,QAAC,SAAI,WAAU,aACb;AAAA,sBAAAA,QAAC,SAAI,WAAU,UACb;AAAA,wBAAAA,QAAC,QAAG,8DAAJ;AAAA;AAAA;AAAA;AAAA,eAAa;AAAA,QACb,gBAAAA,QAAC,OAAE,kIAAH;AAAA;AAAA;AAAA;AAAA,eAAsB;AAAA,WAFxB;AAAA;AAAA;AAAA;AAAA,aAGA;AAAA,MAEA,gBAAAA,QAAC,SAAI,WAAU,kBACb;AAAA,wBAAAA;AAAA,UAAC;AAAA;AAAA,YACC,WAAW,iBAAiB,YAAY,YAAY;AAAA,YACpD,SAAS;AAAA,YACT,UAAU;AAAA,YAET,sBAAY,oCAAc;AAAA;AAAA,UAL7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA;AAAA,QAEC,SACC,gBAAAA,QAAC,SAAI,WAAU,iBAAgB;AAAA;AAAA,UAC1B;AAAA,aADL;AAAA;AAAA;AAAA;AAAA,eAEA;AAAA,WAZJ;AAAA;AAAA;AAAA;AAAA,aAcA;AAAA,MAEC,WAAW,SAAS,KACnB,gBAAAA,QAAC,SAAI,WAAU,mBACb;AAAA,wBAAAA,QAAC,QAAG;AAAA;AAAA,UAAO,WAAW;AAAA,UAAO;AAAA,aAA7B;AAAA;AAAA;AAAA;AAAA,eAA8B;AAAA,QAE7B,WAAW,IAAI,CAAC,UAAU,UACzB,gBAAAA,QAAC,SAAsB,WAAU,iBAC/B;AAAA,0BAAAA,QAAC,SAAI,WAAU,mBACb;AAAA,4BAAAA,QAAC,QAAI,mBAAS,WAAd;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,SAAI,WAAU,iBACb;AAAA,8BAAAA,QAAC,UAAK;AAAA;AAAA,gBAAK,IAAI,KAAK,SAAS,UAAU,EAAE,eAAe,OAAO;AAAA,mBAA/D;AAAA;AAAA;AAAA;AAAA,qBAAiE;AAAA,cACjE,gBAAAA,QAAC,UAAK;AAAA;AAAA,gBAAI,SAAS;AAAA,gBAAkB;AAAA,gBAAE,SAAS;AAAA,gBAAa;AAAA,mBAA7D;AAAA;AAAA;AAAA;AAAA,qBAAiE;AAAA,cACjE,gBAAAA;AAAA,gBAAC;AAAA;AAAA,kBACC,WAAU;AAAA,kBACV,SAAS,MAAM,qBAAqB,KAAK;AAAA,kBACzC,OAAM;AAAA,kBACP;AAAA;AAAA,gBAJD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMA;AAAA,iBATF;AAAA;AAAA;AAAA;AAAA,mBAUA;AAAA,eAZF;AAAA;AAAA;AAAA;AAAA,iBAaA;AAAA,UAEA,gBAAAA,QAAC,SAAI,WAAU,4BACb,0BAAAA,QAAC,WAAM,WAAU,kBACf;AAAA,4BAAAA,QAAC,WACC,0BAAAA,QAAC,QACC;AAAA,8BAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,qBAAM;AAAA,cACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,qBAAM;AAAA,cACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,qBAAM;AAAA,cACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,qBAAM;AAAA,cACN,gBAAAA,QAAC,QAAG,4BAAJ;AAAA;AAAA;AAAA;AAAA,qBAAM;AAAA,cACN,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,qBAAQ;AAAA,cACR,gBAAAA,QAAC,QAAG,kCAAJ;AAAA;AAAA;AAAA;AAAA,qBAAO;AAAA,cACP,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,qBAAQ;AAAA,cACR,gBAAAA,QAAC,QAAG,wCAAJ;AAAA;AAAA;AAAA;AAAA,qBAAQ;AAAA,iBATV;AAAA;AAAA;AAAA;AAAA,mBAUA,KAXF;AAAA;AAAA;AAAA;AAAA,mBAYA;AAAA,YACA,gBAAAA,QAAC,WACE,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,QACC;AAAA,8BAAAA,QAAC,QACC,0BAAAA,QAAC,OAAE,MAAM,KAAK,WAAW,QAAO,UAAS,KAAI,uBAC1C,eAAK,YADR;AAAA;AAAA;AAAA;AAAA,qBAEA,KAHF;AAAA;AAAA;AAAA;AAAA,qBAIA;AAAA,cACA,gBAAAA,QAAC,QAAI,eAAK,gBAAV;AAAA;AAAA;AAAA;AAAA,qBAAuB;AAAA,cACvB,gBAAAA,QAAC,QAAI,eAAK,YAAV;AAAA;AAAA;AAAA;AAAA,qBAAmB;AAAA,cACnB,gBAAAA,QAAC,QAAI,eAAK,aAAV;AAAA;AAAA;AAAA;AAAA,qBAAoB;AAAA,cACpB,gBAAAA,QAAC,QAAI,eAAK,WAAV;AAAA;AAAA;AAAA;AAAA,qBAAkB;AAAA,cAClB,gBAAAA,QAAC,QAAI,eAAK,aAAV;AAAA;AAAA;AAAA;AAAA,qBAAoB;AAAA,cACpB,gBAAAA,QAAC,QAAI,eAAK,qBAAV;AAAA;AAAA;AAAA;AAAA,qBAA4B;AAAA,cAC5B,gBAAAA,QAAC,QAAI,eAAK,uBAAV;AAAA;AAAA;AAAA;AAAA,qBAA8B;AAAA,cAC9B,gBAAAA,QAAC,QAAI,eAAK,eAAV;AAAA;AAAA;AAAA;AAAA,qBAAsB;AAAA,iBAbf,WAAT;AAAA;AAAA;AAAA;AAAA,mBAcA,CACD,KAjBH;AAAA;AAAA;AAAA;AAAA,mBAkBA;AAAA,eAhCF;AAAA;AAAA;AAAA;AAAA,iBAiCA,KAlCF;AAAA;AAAA;AAAA;AAAA,iBAmCA;AAAA,UAGA,gBAAAA,QAAC,aAAQ,WAAU,oBACjB;AAAA,4BAAAA,QAAC,aAAQ,oDAAT;AAAA;AAAA;AAAA;AAAA,mBAAe;AAAA,YACf,gBAAAA,QAAC,SAAI,WAAU,gBACZ,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,SAAoB,WAAU,eAC7B;AAAA,8BAAAA,QAAC,QAAI,eAAK,YAAV;AAAA;AAAA;AAAA;AAAA,qBAAmB;AAAA,cACnB,gBAAAA,QAAC,SAAI,WAAU,eACb;AAAA,gCAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,yBAAW;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAA9B;AAAA;AAAA;AAAA;AAAA,uBAAsC;AAAA,gBACtC,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,yBAAa;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAAhC;AAAA;AAAA;AAAA;AAAA,uBAAyC;AAAA,gBACzC,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,yBAAa;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAAhC;AAAA;AAAA;AAAA;AAAA,uBAA6C;AAAA,gBAC7C,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,+CAAR;AAAA;AAAA;AAAA;AAAA,yBAAc;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAAjC;AAAA;AAAA;AAAA;AAAA,uBAAmD;AAAA,gBAClD,KAAK,SAAS,SAAS,KACtB,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,yBAAW;AAAA,kBAAS;AAAA,kBAAE,KAAK,SAAS,KAAK,IAAI;AAAA,qBAAhD;AAAA;AAAA;AAAA;AAAA,uBAAkD;AAAA,gBAEnD,KAAK,gBAAgB,SAAS,KAC7B,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,yBAAa;AAAA,kBAAS;AAAA,kBAAE,KAAK,gBAAgB,KAAK,IAAI;AAAA,qBAAzD;AAAA;AAAA;AAAA;AAAA,uBAA2D;AAAA,gBAE5D,KAAK,YAAY,SAChB,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,yCAAR;AAAA;AAAA;AAAA;AAAA,yBAAa;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAAhC;AAAA;AAAA;AAAA;AAAA,uBAAwC;AAAA,gBAEzC,KAAK,gBAAgB,SACpB,gBAAAA,QAAC,OAAE;AAAA,kCAAAA,QAAC,YAAO,6BAAR;AAAA;AAAA;AAAA;AAAA,yBAAW;AAAA,kBAAS;AAAA,kBAAE,KAAK;AAAA,qBAA9B;AAAA;AAAA;AAAA;AAAA,uBAA0C;AAAA,mBAf9C;AAAA;AAAA;AAAA;AAAA,qBAiBA;AAAA,iBAnBQ,WAAV;AAAA;AAAA;AAAA;AAAA,mBAoBA,CACD,KAvBH;AAAA;AAAA;AAAA;AAAA,mBAwBA;AAAA,eA1BF;AAAA;AAAA;AAAA;AAAA,iBA2BA;AAAA,aAjFQ,SAAS,IAAnB;AAAA;AAAA;AAAA;AAAA,eAkFA,CACD;AAAA,WAvFH;AAAA;AAAA;AAAA;AAAA,aAwFA;AAAA,SA/GJ;AAAA;AAAA;AAAA;AAAA,WAiHA;AAAA,OA9VF;AAAA;AAAA;AAAA;AAAA,SA+VA;AAEJ;;;ACvaA;AAAA;AAAA,iBAAAK;AAAA;AAAA,SAAS,YAAAC,WAAU,aAAAC,kBAAiB;AA2I5B,mBAAAC,eAAA;AAxIO,SAARC,SAAyB;AAC9B,MAAM,CAAC,YAAY,aAAa,IAAIC,UAAyB,CAAC,CAAC,GACzD,CAAC,WAAW,YAAY,IAAIA,UAAS,EAAK,GAC1C,CAAC,OAAO,QAAQ,IAAIA,UAAwB,IAAI;AAGtD,EAAAC,WAAU,MAAM;AACd,QAAM,kBAAkB,kBAAkB,eAAe;AACzD,kBAAc,eAAe;AAAA,EAC/B,GAAG,CAAC,CAAC,GAGLA,WAAU,MAAM;AACd,sBAAkB,eAAe,UAAU;AAAA,EAC7C,GAAG,CAAC,UAAU,CAAC;AAEf,MAAM,oBAAoB,YAAY;AACpC,iBAAa,EAAI,GACjB,SAAS,IAAI;AAEb,QAAI;AAEF,UAAM,gBAAgB,MAAM,eAAe,SAAS;AAEpD,UAAI,CAAC,cAAc,KAAK;AACtB,cAAM,IAAI,MAAM,4CAAS;AAG3B,cAAQ,IAAI,mCAAU,aAAa;AAGnC,UAAM,WAAW,MAAM,MAAM,wBAAwB;AAAA,QACnD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,EAAE,SAAS,cAAc,KAAK,EAAE,CAAC;AAAA,MACxD,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAM,YAAY,MAAM,SAAS,KAAK;AACtC,cAAM,IAAI,MAAM,UAAU,SAAS,gCAAY,SAAS,QAAQ;AAAA;AAGlE,UAAM,SAAS,MAAM,SAAS,KAAK;AAEnC,UAAI,OAAO,WAAW,OAAO;AAE3B,sBAAc,UAAQ,CAAC,OAAO,MAAM,GAAG,IAAI,CAAC;AAAA;AAE5C,cAAM,IAAI,MAAM,OAAO,SAAS,0BAAM;AAAA,IAG1C,SAASC,QAAP;AACA,cAAQ,MAAM,6BAASA,MAAK,GAC5B,SAASA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK,CAAC;AAAA,IACjE,UAAE;AACA,mBAAa,EAAK;AAAA,IACpB;AAAA,EACF,GAEM,uBAAuB,CAAC,UAAkB;AAC9C,kBAAc,UAAQ,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAC1D,GAEM,cAAc;AAAA,IAClB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,IACd,QAAQ,YAAY,gBAAgB;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS,YAAY,MAAM;AAAA,EAC7B,GAEM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAEM,aAAa;AAAA,IACjB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,EACV,GAEM,YAAY;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,GAEM,cAAc;AAAA,IAClB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,GAEM,aAAa;AAAA,IACjB,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,GAEM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,EACT,GAEM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,EAChB;AAEA,SACE,gBAAAJ,QAAC,SAAI,OAAO,gBACV;AAAA,oBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,UAAU,cAAc,OAAO,GACtD;AAAA,sBAAAA,QAAC,QAAG,OAAO,EAAE,OAAO,WAAW,cAAc,OAAO,GAAG,8DAAvD;AAAA;AAAA;AAAA;AAAA,aAAgE;AAAA,MAChE,gBAAAA,QAAC,OAAE,OAAO,EAAE,OAAO,QAAQ,UAAU,OAAO,GAAG,kIAA/C;AAAA;AAAA;AAAA;AAAA,aAAkE;AAAA,SAFpE;AAAA;AAAA;AAAA;AAAA,WAGA;AAAA,IAEA,gBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,UAAU,cAAc,OAAO,GACtD;AAAA,sBAAAA;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,UAET,sBAAY,oCAAc;AAAA;AAAA,QAL7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,MAEC,SACC,gBAAAA,QAAC,SAAI,OAAO,YAAY;AAAA;AAAA,QACnB;AAAA,WADL;AAAA;AAAA;AAAA;AAAA,aAEA;AAAA,SAZJ;AAAA;AAAA;AAAA;AAAA,WAcA;AAAA,IAEC,WAAW,SAAS,KACnB,gBAAAA,QAAC,SACC;AAAA,sBAAAA,QAAC,QAAG,OAAO,EAAE,OAAO,WAAW,cAAc,OAAO,GAAG;AAAA;AAAA,QAAO,WAAW;AAAA,QAAO;AAAA,WAAhF;AAAA;AAAA;AAAA;AAAA,aAAiF;AAAA,MAEhF,WAAW,IAAI,CAAC,UAAU,UACzB,gBAAAA,QAAC,SAAsB,OAAO,WAC5B;AAAA,wBAAAA,QAAC,SAAI,OAAO,aACV;AAAA,0BAAAA,QAAC,QAAG,OAAO,EAAE,QAAQ,cAAc,OAAO,WAAW,UAAU,OAAO,GAAI,mBAAS,WAAnF;AAAA;AAAA;AAAA;AAAA,iBAA2F;AAAA,UAC3F,gBAAAA,QAAC,SAAI,OAAO,EAAE,SAAS,QAAQ,gBAAgB,iBAAiB,YAAY,UAAU,UAAU,QAAQ,OAAO,OAAO,GACpH;AAAA,4BAAAA,QAAC,SACC;AAAA,8BAAAA,QAAC,UAAK,OAAO,EAAE,aAAa,OAAO,GAAG;AAAA;AAAA,gBAAK,IAAI,KAAK,SAAS,UAAU,EAAE,eAAe,OAAO;AAAA,mBAA/F;AAAA;AAAA;AAAA;AAAA,qBAAiG;AAAA,cACjG,gBAAAA,QAAC,UAAK;AAAA;AAAA,gBAAI,SAAS;AAAA,gBAAkB;AAAA,gBAAE,SAAS;AAAA,gBAAa;AAAA,mBAA7D;AAAA;AAAA;AAAA;AAAA,qBAAiE;AAAA,iBAFnE;AAAA;AAAA;AAAA;AAAA,mBAGA;AAAA,YACA,gBAAAA;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,UAAU;AAAA,gBACZ;AAAA,gBACA,SAAS,MAAM,qBAAqB,KAAK;AAAA,gBACzC,OAAM;AAAA,gBACP;AAAA;AAAA,cAZD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAcA;AAAA,eAnBF;AAAA;AAAA;AAAA;AAAA,iBAoBA;AAAA,aAtBF;AAAA;AAAA;AAAA;AAAA,eAuBA;AAAA,QAEA,gBAAAA,QAAC,SAAI,OAAO,EAAE,WAAW,QAAQ,SAAS,SAAS,GACjD,0BAAAA,QAAC,WAAM,OAAO,YACZ;AAAA,0BAAAA,QAAC,WACC,0BAAAA,QAAC,QACC;AAAA,4BAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,4BAApB;AAAA;AAAA;AAAA;AAAA,mBAAsB;AAAA,YACtB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,mBAAwB;AAAA,YACxB,gBAAAA,QAAC,QAAG,OAAO,SAAS,kCAApB;AAAA;AAAA;AAAA;AAAA,mBAAuB;AAAA,YACvB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,mBAAwB;AAAA,YACxB,gBAAAA,QAAC,QAAG,OAAO,SAAS,wCAApB;AAAA;AAAA;AAAA;AAAA,mBAAwB;AAAA,eAT1B;AAAA;AAAA;AAAA;AAAA,iBAUA,KAXF;AAAA;AAAA;AAAA;AAAA,iBAYA;AAAA,UACA,gBAAAA,QAAC,WACE,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,QACC;AAAA,4BAAAA,QAAC,QAAG,OAAO,SACT,0BAAAA,QAAC,OAAE,MAAM,KAAK,WAAW,QAAO,UAAS,KAAI,uBAAsB,OAAO,EAAE,OAAO,WAAW,gBAAgB,OAAO,GAClH,eAAK,YADR;AAAA;AAAA;AAAA;AAAA,mBAEA,KAHF;AAAA;AAAA;AAAA;AAAA,mBAIA;AAAA,YACA,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,gBAA1B;AAAA;AAAA;AAAA;AAAA,mBAAuC;AAAA,YACvC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,YAA1B;AAAA;AAAA;AAAA;AAAA,mBAAmC;AAAA,YACnC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,aAA1B;AAAA;AAAA;AAAA;AAAA,mBAAoC;AAAA,YACpC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,WAA1B;AAAA;AAAA;AAAA;AAAA,mBAAkC;AAAA,YAClC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,aAA1B;AAAA;AAAA;AAAA;AAAA,mBAAoC;AAAA,YACpC,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,qBAA1B;AAAA;AAAA;AAAA;AAAA,mBAA4C;AAAA,YAC5C,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,uBAA1B;AAAA;AAAA;AAAA;AAAA,mBAA8C;AAAA,YAC9C,gBAAAA,QAAC,QAAG,OAAO,SAAU,eAAK,eAA1B;AAAA;AAAA;AAAA;AAAA,mBAAsC;AAAA,eAb/B,WAAT;AAAA;AAAA;AAAA;AAAA,iBAcA,CACD,KAjBH;AAAA;AAAA;AAAA;AAAA,iBAkBA;AAAA,aAhCF;AAAA;AAAA;AAAA;AAAA,eAiCA,KAlCF;AAAA;AAAA;AAAA;AAAA,eAmCA;AAAA,QAEA,gBAAAA,QAAC,aAAQ,OAAO,EAAE,SAAS,QAAQ,WAAW,iBAAiB,GAC7D;AAAA,0BAAAA,QAAC,aAAQ,OAAO,EAAE,QAAQ,WAAW,YAAY,KAAK,OAAO,WAAW,cAAc,OAAO,GAAG,oDAAhG;AAAA;AAAA;AAAA;AAAA,iBAEA;AAAA,UACA,gBAAAA,QAAC,SAAI,OAAO,EAAE,SAAS,QAAQ,qBAAqB,wCAAwC,KAAK,QAAQ,WAAW,OAAO,GACxH,mBAAS,WAAW,IAAI,CAAC,MAAM,cAC9B,gBAAAA,QAAC,SAAoB,OAAO;AAAA,YAC1B,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,cAAc;AAAA,YACd,YAAY;AAAA,UACd,GACE;AAAA,4BAAAA,QAAC,QAAG,OAAO,EAAE,QAAQ,cAAc,OAAO,UAAU,GAAI,eAAK,YAA7D;AAAA;AAAA;AAAA;AAAA,mBAAsE;AAAA,YACtE,gBAAAA,QAAC,SACC;AAAA,8BAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,6BAAlC;AAAA;AAAA;AAAA;AAAA,uBAAqC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBADvD;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cACA,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,yCAAlC;AAAA;AAAA;AAAA;AAAA,uBAAuC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBADzD;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cACA,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,yCAAlC;AAAA;AAAA;AAAA;AAAA,uBAAuC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBADzD;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cACA,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,+CAAlC;AAAA;AAAA;AAAA;AAAA,uBAAwC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBAD1D;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cACC,KAAK,SAAS,SAAS,KACtB,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,6BAAlC;AAAA;AAAA;AAAA;AAAA,uBAAqC;AAAA,gBAAS;AAAA,gBAAE,KAAK,SAAS,KAAK,IAAI;AAAA,mBADzE;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cAED,KAAK,gBAAgB,SAAS,KAC7B,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,yCAAlC;AAAA;AAAA;AAAA;AAAA,uBAAuC;AAAA,gBAAS;AAAA,gBAAE,KAAK,gBAAgB,KAAK,IAAI;AAAA,mBADlF;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cAED,KAAK,YAAY,SAChB,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,yCAAlC;AAAA;AAAA;AAAA;AAAA,uBAAuC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBADzD;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,cAED,KAAK,gBAAgB,SACpB,gBAAAA,QAAC,OAAE,OAAO,EAAE,QAAQ,SAAS,UAAU,QAAQ,YAAY,MAAM,GAC/D;AAAA,gCAAAA,QAAC,YAAO,OAAO,EAAE,OAAO,OAAO,GAAG,6BAAlC;AAAA;AAAA;AAAA;AAAA,uBAAqC;AAAA,gBAAS;AAAA,gBAAE,KAAK;AAAA,mBADvD;AAAA;AAAA;AAAA;AAAA,qBAEA;AAAA,iBA/BJ;AAAA;AAAA;AAAA;AAAA,mBAiCA;AAAA,eAxCQ,WAAV;AAAA;AAAA;AAAA;AAAA,iBAyCA,CACD,KA5CH;AAAA;AAAA;AAAA;AAAA,iBA6CA;AAAA,aAjDF;AAAA;AAAA;AAAA;AAAA,eAkDA;AAAA,WAjHQ,SAAS,IAAnB;AAAA;AAAA;AAAA;AAAA,aAkHA,CACD;AAAA,SAvHH;AAAA;AAAA;AAAA;AAAA,WAwHA;AAAA,OA/IJ;AAAA;AAAA;AAAA;AAAA,SAiJA;AAEJ;;;AC5RA,IAAO,0BAAQ,EAAC,OAAQ,EAAC,QAAS,mCAAkC,SAAU,CAAC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,oCAAmC,kCAAkC,EAAC,GAAE,QAAS,EAAC,MAAO,EAAC,IAAK,QAAO,UAAW,QAAU,MAAO,IAAG,OAAQ,QAAU,eAAgB,QAAU,QAAS,2BAA0B,SAAU,QAAU,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,iBAAgB,EAAC,IAAK,iBAAgB,UAAW,QAAO,MAAO,QAAU,OAAQ,IAAK,eAAgB,QAAU,QAAS,oCAAmC,SAAU,CAAC,kCAAkC,GAAE,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,qBAAoB,EAAC,IAAK,qBAAoB,UAAW,QAAO,MAAO,QAAU,OAAQ,QAAU,eAAgB,QAAU,QAAS,wCAAuC,SAAU,CAAC,kCAAkC,GAAE,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,qBAAoB,EAAC,IAAK,qBAAoB,UAAW,QAAO,MAAO,QAAU,OAAQ,QAAU,eAAgB,QAAU,QAAS,wCAAuC,SAAU,CAAC,kCAAkC,GAAE,WAAY,IAAM,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,GAAE,8BAA6B,EAAC,IAAK,8BAA6B,UAAW,QAAO,MAAO,uBAAsB,OAAQ,QAAU,eAAgB,QAAU,QAAS,iDAAgD,SAAU,QAAU,WAAY,IAAK,WAAY,IAAM,iBAAkB,IAAM,iBAAkB,IAAM,kBAAmB,GAAK,EAAC,GAAE,SAAU,YAAW,KAAM,EAAC,SAAU,qCAAoC,WAAY,cAAa,GAAE,KAAM,8BAA6B;;;ACOzhE,IAAM,OAAO,eAEP,uBAAuB,iBACvB,SAAS,EAAC,mBAAoB,IAAM,sBAAuB,IAAM,qBAAsB,IAAM,gBAAiB,IAAM,gBAAiB,IAAM,uBAAwB,IAAM,uBAAwB,GAAK,GACtM,aAAa,WACb,QAAQ,EAAE,QAAQ,qBAAY,GAC9B,SAAS;AAAA,EACpB,MAAQ;AAAA,IACN,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,8BAA8B;AAAA,IAC1B,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,qBAAqB;AAAA,IACjB,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,qBAAqB;AAAA,IACjB,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AAAA,EACF,iBAAiB;AAAA,IACb,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,EACV;AACF;", "names": ["jsxDEV", "jsxDEV", "error", "Index", "useState", "useEffect", "jsxDEV", "Index", "useState", "useEffect", "error", "Index", "useState", "useEffect", "jsxDEV", "Index", "useState", "useEffect", "error"]}