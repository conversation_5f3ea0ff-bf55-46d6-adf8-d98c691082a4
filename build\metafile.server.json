{"inputs": {"app/entry.server.tsx": {"bytes": 597, "imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/root.tsx": {"bytes": 1142, "imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/utils/server-search-simple.ts": {"bytes": 3990, "imports": [{"path": "exa-js", "kind": "import-statement", "external": true}], "format": "esm"}, "app/routes/api.search-property.ts": {"bytes": 1076, "imports": [{"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "app/utils/server-search-simple.ts", "kind": "import-statement", "original": "~/utils/server-search-simple"}], "format": "esm"}, "app/utils/property-search.ts": {"bytes": 2799, "imports": [], "format": "esm"}, "app/routes/_index-old.tsx": {"bytes": 12339, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "app/utils/property-search.ts", "kind": "import-statement", "original": "~/utils/property-search"}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/routes/_index_new.tsx": {"bytes": 12606, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "app/utils/property-search.ts", "kind": "import-statement", "original": "~/utils/property-search"}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "app/routes/_index.tsx": {"bytes": 11071, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "app/utils/property-search.ts", "kind": "import-statement", "original": "~/utils/property-search"}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "format": "esm"}, "server-assets-manifest:@remix-run/dev/assets-manifest": {"bytes": 2084, "imports": [], "format": "esm"}, "server-entry-module:@remix-run/dev/server-build": {"bytes": 1764, "imports": [{"path": "app/entry.server.tsx", "kind": "import-statement", "original": "C:\\Users\\<USER>\\projects\\property_helper\\app\\entry.server.tsx"}, {"path": "app/root.tsx", "kind": "import-statement", "original": "./root.tsx"}, {"path": "app/routes/api.search-property.ts", "kind": "import-statement", "original": "./routes/api.search-property.ts"}, {"path": "app/routes/_index-old.tsx", "kind": "import-statement", "original": "./routes/_index-old.tsx"}, {"path": "app/routes/_index_new.tsx", "kind": "import-statement", "original": "./routes/_index_new.tsx"}, {"path": "app/routes/_index.tsx", "kind": "import-statement", "original": "./routes/_index.tsx"}, {"path": "server-assets-manifest:@remix-run/dev/assets-manifest", "kind": "import-statement", "original": "@remix-run/dev/assets-manifest"}], "format": "esm"}, "<stdin>": {"bytes": 44, "imports": [{"path": "server-entry-module:@remix-run/dev/server-build", "kind": "import-statement", "original": "@remix-run/dev/server-build"}], "format": "esm"}}, "outputs": {"build/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 85491}, "build/index.js": {"imports": [{"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react-dom/server", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "@remix-run/react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "@remix-run/node", "kind": "import-statement", "external": true}, {"path": "exa-js", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react/jsx-dev-runtime", "kind": "import-statement", "external": true}], "exports": ["assets", "assetsBuildDirectory", "entry", "future", "mode", "publicPath", "routes"], "entryPoint": "<stdin>", "inputs": {"app/entry.server.tsx": {"bytesInOutput": 734}, "app/root.tsx": {"bytesInOutput": 2828}, "app/routes/api.search-property.ts": {"bytesInOutput": 913}, "app/utils/server-search-simple.ts": {"bytesInOutput": 3569}, "app/routes/_index-old.tsx": {"bytesInOutput": 23358}, "app/utils/property-search.ts": {"bytesInOutput": 1070}, "app/routes/_index_new.tsx": {"bytesInOutput": 24326}, "app/routes/_index.tsx": {"bytesInOutput": 21397}, "server-assets-manifest:@remix-run/dev/assets-manifest": {"bytesInOutput": 2003}, "server-entry-module:@remix-run/dev/server-build": {"bytesInOutput": 1191}, "<stdin>": {"bytesInOutput": 0}}, "bytes": 82133}}}